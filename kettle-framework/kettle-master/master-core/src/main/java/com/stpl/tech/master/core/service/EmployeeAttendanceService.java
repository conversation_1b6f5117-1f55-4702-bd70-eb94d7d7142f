package com.stpl.tech.master.core.service;

import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingRequest;
import com.stpl.tech.master.domain.model.EmployeeEligibilityMappingResponse;
import org.springframework.web.servlet.View;

import java.util.List;

/**
 * Service interface for Employee Eligibility Mapping operations
 */
public interface EmployeeAttendanceService {

    /**
     * Save employee eligibility mapping
     * @param request mapping request
     * @param createdBy user who created the mapping
     * @return saved mapping response
     * @throws com.stpl.tech.master.core.exception.DataUpdationException if save fails
     */

    EmployeeEligibilityMappingResponse saveEmployeeAttendanceMapping(EmployeeEligibilityMappingRequest request, String createdBy) throws DataUpdationException;

    /**
     * Get mappings by employee ID
     * @param empId employee ID
     * @return list of mappings
     */
    List<EmployeeEligibilityMappingResponse> getEligibilityAttendanceMappingsByEmpId(String empId);

    View prepareEmployeeMappingExcel(List<Long> empIds) throws Exception;

    /**
     * Download template for bulk upload
     * @return Excel template view
     * @throws Exception if template generation fails
     */
    View downloadBulkUploadTemplate() throws Exception;

    /**
     * Process bulk upload data with backend validation
     * @param bulkData list of mapping requests
     * @param createdBy user who created the mappings
     * @return bulk upload response with validation results
     * @throws Exception if processing fails
     */
    BulkUploadResponse processBulkUpload(List<EmployeeEligibilityMappingRequest> bulkData, String createdBy) throws Exception;
}
