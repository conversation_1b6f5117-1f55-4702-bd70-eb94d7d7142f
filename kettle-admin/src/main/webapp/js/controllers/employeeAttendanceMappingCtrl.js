adminapp.controller("employeeAttendanceMappingCtrl", function($scope, $http, $location, $window, $rootScope, $timeout, AuthService, AppUtil ,toastService) {

    $scope.init = function() {
        $rootScope.showFullScreenLoader = true;

        // Initialize scope variables
        $scope.employees = [];
        $scope.filteredEmployees = [];
        $scope.paginatedEmployees = [];
        $scope.selectedEmployees = [];
        $scope.units = [];
        $scope.allUnits = [];
        $scope.filteredUnits = [];
        $scope.departments = [];
        $scope.regions = [];
        $scope.cities = [];
        $scope.categories = [];
        $scope.managers = [];
        $scope.designations = [];
        $scope.filteredMappings = [];


        // Pagination variables
        $scope.currentPage = 1;
        $scope.itemsPerPage = 20;
        $scope.totalPages = 1;

        // Searchable dropdown variables
        $scope.categorySearch = '';
        $scope.citySearch = '';
        $scope.regionSearch = '';
        $scope.unitSearch = '';
        $scope.unitZoneSearch = '';
        $scope.designationSearch = '';
        $scope.departmentSearch = '';
        $scope.cityMappingSearch = '';
        $scope.regionMappingSearch = '';
        $scope.filteredCategories = [];
        $scope.filteredUnitZones = [];
        $scope.filteredCities = [];
        $scope.filteredRegions = [];
        $scope.filteredCitiesMapping = [];
        $scope.filteredRegionsMapping = [];
        $scope.filteredUnitsDropdown = [];
        $scope.filteredDesignations = [];
        $scope.filteredDepartments = [];
        $scope.showCategoryDropdown = false;
        $scope.showUnitZoneDropdown = false;
        $scope.showCityDropdown = false;
        $scope.showRegionDropdown = false;
        $scope.showUnitDropdown = false;
        $scope.showDesignationDropdown = false;
        $scope.showDepartmentDropdown = false;
        $scope.showCityMappingDropdown = false;
        $scope.showRegionMappingDropdown = false;
        $scope.unitZones = [];

        // Filter variables
        $scope.filters = {
            designation: 'All Designations',
            department: 'All Departments'
        };

        // Unit mapping filters
        $scope.unitFilters = {
            category: null,
            city: null,
            region: null,
            selectedUnitId: null,
            selectedUnitName: null,
            unitZone: null
        };

        // Search variables
        $scope.searchText = '';

        // Step management
        $scope.currentStep = 1;
        $scope.mappings = [];
        $scope.selectedMappingType = 'unit';
        $scope.eligibilityType = 'attendance';

        // City and Region mapping values
        $scope.cityMappingValue = '';
        $scope.regionMappingValue = '';

        // Apply to dropdown - default to 'all selected employees'
        $scope.applyTo = 'all';

        // Employee mapping view modal
        $scope.showEmployeeMappingModal = false;
        $scope.selectedEmployeeForView = null;
        $scope.employeeMappings = [];
        $scope.filteredEmployeeMappings = [];
        $scope.loadingEmployeeMappings = false;
        $scope.employeeMappingSearch = '';

        // Bulk upload variables
        $scope.showBulkUploadModal = false;
        $scope.bulkUploadData = [];
        $scope.filteredBulkUploadData = [];
        $scope.processingBulkUpload = false;
        $scope.bulkUploadFilter = 'all';
        $scope.bulkUploadResponse = null;
        $scope.originalBulkData = []; // Store original data for processing

        // Load initial data (not employees and units - they will be loaded on demand)
        $scope.loadDepartments();
        $scope.loadDesignations();
        $scope.loadRegions();
        $scope.loadCities();
        $scope.loadCategories();
        $scope.loadManagers();
        $scope.loadUnitZones();

        // Initialize empty arrays for employees and units
        $scope.employees = [];
        $scope.filteredEmployees = [];
        $scope.allUnits = [];
        $scope.filteredUnits = [];
    };

    // Load Employees
    $scope.loadEmployees = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.users
        }).then(function success(response) {
            // Filter to get only ACTIVE employees
            $scope.employees = (response.data || []).filter(function(employee) {
                return employee.status === 'ACTIVE';
            });
            // Apply current filters after loading
            $scope.applyFilters();
        }, function error(response) {
            console.log("Error loading employees:", response);
            $scope.employees = [];
            $scope.filteredEmployees = [];
        });
    };

    $scope.isMultiMappingAllowed = function() {
        return $scope.selectedEmployees.every(function(emp) {
            return emp.maxAllocatedUnits === -1;
        });
    };


    // Load All Units (called when dropdown filters change)
    $scope.loadAllUnits = function() {
        if ($scope.allUnits.length === 0) {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.unitMetaData.allUnitsList
            }).then(function success(response) {
                $scope.allUnits = response.data || [];
                $scope.filterUnits();
                console.log('All units loaded:', $scope.allUnits);
            }, function error(response) {
                console.log("Error loading units:", response);
                $scope.allUnits = [];
                $scope.filteredUnits = [];
            });
        } else {
            $scope.filterUnits();
        }
    };

    // Load Departments
    $scope.loadDepartments = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.departments
        }).then(function success(response) {
            $scope.departments = response.data || [];
        }, function error(response) {
            console.log("Error loading departments:", response);
            $scope.departments = [];
        });
    };

    // Load Designations
    $scope.loadDesignations = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.designations
        }).then(function success(response) {
            $scope.designations = response.data || [];
        }, function error(response) {
            console.log("Error loading designations:", response);
            $scope.designations = [];
        });
    };

    // Load Regions
    $scope.loadRegions = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.regions = response.data || [];
        }, function error(response) {
            console.log("Error loading regions:", response);
            $scope.regions = [];
        });
    };

    // Load Cities
    $scope.loadCities = function () {
        $rootScope.showFullScreenLoader = true;
        $http({
            method: 'POST',
            url: AppUtil.restUrls.unitMetaData.unitCities
        }).then(function (response) {
            $scope.cityList = response.data || [];
            $scope.filteredCities = $scope.cityList.slice(); // ✅ This is must for filtering

            $rootScope.showFullScreenLoader = false;
        }, function (err) {
            console.log('Error in getting response', err);
            $scope.cityList = [];
            $scope.filteredCities = [];
            $rootScope.showFullScreenLoader = false;
        });
    };

    // Load Managers
    $scope.loadManagers = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.managers
        }).then(function success(response) {
            $scope.managers = response.data || [];
        }, function error(response) {
            console.log("Error loading managers:", response);
            $scope.managers = [];
        });
    };

    // Load Categories
    $scope.loadCategories = function() {
        // Define common unit categories
        $scope.categories = [
            { name: 'CAFE', value: 'CAFE' },
            { name: 'WAREHOUSE', value: 'WAREHOUSE' },
            { name: 'KITCHEN', value: 'KITCHEN' },
            { name: 'OFFICE', value: 'OFFICE' },
            { name: 'DELIVERY', value: 'DELIVERY' },
            { name: 'COD', value: 'COD' },
            { name: 'TAKE_AWAY', value: 'TAKE_AWAY' }
        ];
    };

    $scope.loadUnitZones = function() {
        // defining unit zones
        $scope.unitZones     = [
            { name: 'NORTH', value: 'NORTH' },
            { name: 'SOUTH', value: 'SOUTH' },
            { name: 'WEST', value: 'WEST' },
            { name: 'EAST', value: 'EAST' }
        ];
    }

    // Filter units based on selected criteria
    $scope.filterUnits = function() {
        $scope.filteredUnits = $scope.allUnits.filter(function(unit) {
            var matchesCategory = !$scope.unitFilters.category || unit.category === $scope.unitFilters.category;
            var matchesCity = !$scope.unitFilters.city || (unit.city && unit.city.toLowerCase().includes($scope.unitFilters.city.toLowerCase()));
            var matchesRegion = !$scope.unitFilters.region || (unit.region && unit.region.toLowerCase().includes($scope.unitFilters.region.toLowerCase()));
            var matchesUnitZone = !$scope.unitFilters.unitZone || (unit.unitZone && unit.unitZone.toLowerCase().includes($scope.unitFilters.unitZone.toLowerCase()));

            return matchesCategory && matchesCity && matchesRegion && matchesUnitZone;
        });

        // Reset unit selections when filters change
        $scope.unitFilters.selectedUnitId = null;
        $scope.unitFilters.selectedUnitName = null;

        console.log('Filtered units:', $scope.filteredUnits);
    };

    // Handle unit filter changes
    $scope.onUnitFilterChange = function() {
        $scope.loadAllUnits();
    };

    // Clear all unit filters
    $scope.clearUnitFilters = function() {
        $scope.unitFilters = {
            category: null,
            city: null,
            region: null,
            selectedUnitId: null,
            selectedUnitName: null,
            unitZone: null
        };

        // Clear search inputs
        $scope.categorySearch = '';
        $scope.citySearch = '';
        $scope.regionSearch = '';
        $scope.unitSearch = '';
        $scope.unitZoneSearch = '';

        // Reset filtered arrays
        if ($scope.categories) $scope.filteredCategories = $scope.categories.slice();
        if ($scope.unitZones) $scope.filteredUnitZones = $scope.unitZones.slice();
        if ($scope.cities) $scope.filteredCities = $scope.cities.slice();
        if ($scope.regions) $scope.filteredRegions = $scope.regions.slice();
        $scope.filteredUnits = $scope.allUnits.slice();
        $scope.filteredUnitsDropdown = $scope.allUnits.slice();
    };

    // Sync unit selection between ID and Name dropdowns
    $scope.syncUnitSelection = function(changedField) {
        if (changedField === 'id' && $scope.unitFilters.selectedUnitId) {
            // Find the unit by ID and set the name
            var selectedUnit = $scope.filteredUnits.find(function(unit) {
                return unit.id == $scope.unitFilters.selectedUnitId;
            });
            if (selectedUnit) {
                $scope.unitFilters.selectedUnitName = selectedUnit.name;
            }
        } else if (changedField === 'name' && $scope.unitFilters.selectedUnitName) {
            // Find the unit by name and set the ID
            var selectedUnit = $scope.filteredUnits.find(function(unit) {
                return unit.name === $scope.unitFilters.selectedUnitName;
            });
            if (selectedUnit) {
                $scope.unitFilters.selectedUnitId = selectedUnit.id;
            }
        }
    };

    // Search and Filter Functions
    $scope.searchEmployees = function() {
        // Load employees if not already loaded
        if ($scope.employees.length === 0) {
            $scope.loadEmployees();
            return;
        }

        $scope.filteredEmployees = $scope.employees.filter(function(employee) {
            var empDesignation = employee.designation || employee.designationName || '';
            var empDepartment = employee.department || employee.departmentName || '';

            var matchesDesignation = $scope.filters.designation === 'All Designations' ||
                                    empDesignation === $scope.filters.designation;
            var matchesDepartment = $scope.filters.department === 'All Departments' ||
                                  empDepartment === $scope.filters.department;
            var matchesSearch = !$scope.searchText ||
                              (employee.name && employee.name.toLowerCase().includes($scope.searchText.toLowerCase())) ||
                              (employee.employeeId && employee.employeeId.toString().includes($scope.searchText)) ||
                              (employee.employeeCode && employee.employeeCode.toString().includes($scope.searchText)) ||
                              (employee.id && employee.id.toString().includes($scope.searchText));

            return matchesDesignation && matchesDepartment && matchesSearch;
        });

        // Reset to first page when search changes
        $scope.currentPage = 1;

        // Load mapping counts for filtered employees
//        $scope.loadMappingCountsForEmployees($scope.filteredEmployees);
    };

    // Apply filters after employees are loaded
    $scope.applyFilters = function() {
        if ($scope.employees.length > 0) {
            $scope.searchEmployees();
        }
    };


    // Pagination Functions
    $scope.updatePagination = function() {
        $scope.totalPages = Math.ceil($scope.filteredEmployees.length / $scope.itemsPerPage);
        if ($scope.totalPages === 0) $scope.totalPages = 1;

        var startIndex = ($scope.currentPage - 1) * $scope.itemsPerPage;
        var endIndex = startIndex + $scope.itemsPerPage;
        $scope.paginatedEmployees = $scope.filteredEmployees.slice(startIndex, endIndex);
    };

    $scope.goToPage = function(page) {
        if (page >= 1 && page <= $scope.totalPages) {
            $scope.currentPage = page;
            $scope.updatePagination();
        }
    };

    $scope.getVisiblePages = function() {
        var pages = [];
        var start = Math.max(1, $scope.currentPage - 1);
        var end = Math.min($scope.totalPages, start + 2);

        // Adjust start if we're near the end
        if (end - start < 2) {
            start = Math.max(1, end - 2);
        }

        for (var i = start; i <= end; i++) {
            pages.push(i);
        }

        // Add ellipsis and last page if needed
        if (end < $scope.totalPages) {
            if (end < $scope.totalPages - 1) {
                pages.push('...');
            }
            pages.push($scope.totalPages);
        }

        return pages;
    };

    $scope.getStartIndex = function() {
        return ($scope.currentPage - 1) * $scope.itemsPerPage + 1;
    };

    $scope.getEndIndex = function() {
        return Math.min($scope.currentPage * $scope.itemsPerPage, $scope.filteredEmployees.length);
    };

    // Employee Selection Functions
    $scope.toggleEmployeeSelection = function(employee) {
        var index = $scope.selectedEmployees.findIndex(function(emp) {
            return emp.id === employee.id;
        });

        if (index > -1) {
            $scope.selectedEmployees.splice(index, 1);
        } else {
            $scope.selectedEmployees.push(employee);
        }
        $scope.updateSelectedCount();
    };

    $scope.isEmployeeSelected = function(employee) {
        return $scope.selectedEmployees.some(function(emp) {
            return emp.id === employee.id;
        });
    };

    $scope.toggleSelectAll = function() {
        if ($scope.selectedEmployees.length === $scope.filteredEmployees.length && $scope.filteredEmployees.length > 0) {
            // Deselect all filtered employees
            $scope.selectedEmployees = [];
        } else {
            // Select all filtered employees
            $scope.selectedEmployees = $scope.filteredEmployees.slice();
        }
        $scope.updateSelectedCount();
    };

    $scope.updateSelectedCount = function() {
        $scope.selectedCount = $scope.selectedEmployees.length;
    };

    // View employee mappings
    $scope.viewEmployeeMappings = function(employee) {
        $scope.selectedEmployeeForView = employee;
        $scope.loadingEmployeeMappings = true;
        $scope.showEmployeeMappingModal = true;
        $scope.employeeMappings = [];

        var empId = employee.employeeId || employee.id.toString();

        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.employeeAttendanceMapping + '/' + empId
        }).then(function success(response) {
            $scope.employeeMappings = response.data || [];
            $scope.loadingEmployeeMappings = false;

            // Enhance mappings with readable names
            $scope.employeeMappings.forEach(function(mapping) {
                if (mapping.mappingType === 'UNIT') {
                    // Find unit name from allUnits
                    var unit = $scope.allUnits.find(function(u) {
                        return u.id.toString() === mapping.value;
                    });
                    mapping.displayValue = unit ? unit.name + ' (' + mapping.value + ')' : mapping.value;
                } else {
                    mapping.displayValue = mapping.value;
                }
            });

            // Initialize filtered mappings
            $scope.filteredEmployeeMappings = $scope.employeeMappings.slice();

        }, function error(response) {
            console.error('Error loading employee mappings:', response);
            $scope.employeeMappings = [];
            $scope.filteredEmployeeMappings = [];
            $scope.loadingEmployeeMappings = false;
            toastService.error('Failed to load employee mappings.');
        });
    };



    // Close employee mapping modal
    $scope.closeEmployeeMappingModal = function() {
        $scope.showEmployeeMappingModal = false;
        $scope.selectedEmployeeForView = null;
        $scope.employeeMappings = [];
    };

    // Check if employee has mappings (for showing view icon)
    $scope.hasEmployeeMappings = function(employee) {
        // This could be enhanced to cache mapping status
        return true; // Show view icon for all employees for now
    };

    // Step Navigation Functions
    $scope.goToMapping = function() {
        if ($scope.selectedEmployees.length === 0) {
            toastService.error('Please select at least one employee to proceed.');
            return;
        }
        $scope.currentStep = 2;
        $scope.updateStepIndicator();
        $timeout(() => {
            window.scrollTo(0, 0);
        });
    };

    $scope.goToReview = function() {
        if ($scope.mappings.length === 0) {
            toastService.error('Please add at least one mapping before proceeding to review.');
            return;
        }
        $scope.currentStep = 3;
        $scope.updateStepIndicator();
        $timeout(() => {
            window.scrollTo(0, 0);
        });
    };

    $scope.goBackToEmployeeSelection = function () {
        $scope.currentStep = 1;
        $scope.updateStepIndicator();
        $timeout(() => {
            window.scrollTo(0, 0);
        });
    };

    $scope.goBackToMapping = function () {
        $scope.currentStep = 2;
        $scope.updateStepIndicator();
        $timeout(() => {
            window.scrollTo(0, 0);
        });
    };
    $scope.updateStepIndicator = function() {
        // Update step indicator classes
        var steps = document.querySelectorAll('.step');
        steps.forEach(function(step, index) {
            if (index + 1 <= $scope.currentStep) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
        // Show/hide sections
        var employeeSection = document.getElementById('employeeSelection');
        var mappingSection = document.getElementById('mappingSection');
        var reviewSection = document.getElementById('reviewSection');

        if ($scope.currentStep === 1) {
            employeeSection.style.display = 'block';
            mappingSection.style.display = 'none';
            if (reviewSection) reviewSection.style.display = 'none';
        } else if ($scope.currentStep === 2) {
            employeeSection.style.display = 'none';
            mappingSection.style.display = 'block';
            if (reviewSection) reviewSection.style.display = 'none';
        } else if ($scope.currentStep === 3) {
            employeeSection.style.display = 'none';
            mappingSection.style.display = 'none';
            if (reviewSection) reviewSection.style.display = 'block';
        }
    };

    // Mapping Functions
    $scope.switchEligibilityTab = function(type) {
        $scope.eligibilityType = type;
        // Reset mappings when switching eligibility type
        $scope.mappings = [];
        $scope.updateMappingsList();

        // Reset mapping form values
        $scope.cityMappingValue = '';
        $scope.regionMappingValue = '';
        $scope.unitFilters.selectedUnitId = null;
        $scope.unitFilters.selectedUnitName = null;

        // Update tab classes
        var tabs = document.querySelectorAll('.tab-btn');
        tabs.forEach(function(tab) {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');
    };

    $scope.switchMappingTab = function(type) {
        $scope.selectedMappingType = type;
        // Update tab classes
        var tabs = document.querySelectorAll('.mapping-tab');
        tabs.forEach(function(tab) {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');

        // Update form title
        var title = document.querySelector('.search-title');
        if (title) {
            switch(type) {
                case 'unit':
                    title.textContent = '🏢 Configure Unit Mapping';
                    break;
                case 'city':
                    title.textContent = '🏙️ Configure City Mapping';
                    break;
                case 'region':
                    title.textContent = '🌍 Configure Region Mapping';
                    break;
            }
        }
    };

    $scope.addMapping = function() {
        var applyTo = $scope.applyTo;
        // For unit mapping, use the selected unit from dropdowns
        var mappingValue, mappingName;
        if ($scope.selectedMappingType === 'unit') {
            if (!$scope.unitFilters.selectedUnitId || !$scope.unitFilters.selectedUnitName) {
                toastService.error('Please select Units');
                return;
            }
            mappingValue = $scope.unitFilters.selectedUnitId;
            mappingName = $scope.unitFilters.selectedUnitName;
        } else if ($scope.selectedMappingType === 'city') {
            // For city mapping, use the selected city value
            if (!$scope.cityMappingValue) {
                alert('Please select a city.');
                return;
            }
            mappingValue = $scope.cityMappingValue;
            mappingName = $scope.cityMappingValue;
        } else if ($scope.selectedMappingType === 'region') {
            // For region mapping, use the selected region value
            if (!$scope.regionMappingValue) {
                alert('Please select a region.');
                return;
            }
            mappingValue = $scope.regionMappingValue;
            mappingName = $scope.regionMappingValue;
        }

        // Determine which employees to apply the mapping to
        var employeesToMap = [];
        if (applyTo === 'all') {
            employeesToMap = $scope.selectedEmployees.slice();
        } else if (applyTo === 'individual') {
            // Get only the employees that are checked for individual mapping
            employeesToMap = $scope.selectedEmployees.filter(function(emp) {
                return emp.selectedForMapping;
            });

            if (employeesToMap.length === 0) {
                toastService.error('Please select at least one employee for individual mapping.');
                return;
            }
        }

        var mapping = {
            type: $scope.selectedMappingType,
            value: mappingValue,
            name: mappingName,
            applyTo: applyTo,
            employees: employeesToMap,
            eligibilityType: $scope.eligibilityType
        };

        $scope.mappings.push(mapping);
        $scope.updateMappingsList();
        // Clear form
        if ($scope.selectedMappingType === 'unit') {
            $scope.unitFilters.selectedUnitId = null;
            $scope.unitFilters.selectedUnitName = null;
        } else if ($scope.selectedMappingType === 'city') {
            $scope.cityMappingValue = '';
        } else if ($scope.selectedMappingType === 'region') {
            $scope.regionMappingValue = '';
        }
        $scope.applyTo = 'all';

        // Clear individual selection checkboxes
        $scope.selectedEmployees.forEach(function(emp) {
            emp.selectedForMapping = false;
        });
    };

    $scope.updateMappingsList = function() {
        // Update mappings count
        var countElement = document.querySelector('.mappings-count');
        if (countElement) {
            countElement.textContent = $scope.mappings.length + ' mappings';
        }
    };

    $scope.removeMapping = function(index) {
        $scope.mappings.splice(index, 1);
        $scope.updateMappingsList();
    };

    $scope.filterMappings = function() {
        var searchText = document.getElementById('mappingSearch').value.toLowerCase();
        var filterType = document.getElementById('mappingFilter').value;

        var mappingItems = document.querySelectorAll('.mapping-item');
        mappingItems.forEach(function(item) {
            var itemType = item.getAttribute('data-type');
            var itemValue = item.getAttribute('data-value').toLowerCase();
            var itemEmployees = item.getAttribute('data-employees').toLowerCase();

            var matchesSearch = !searchText || itemValue.includes(searchText) || itemEmployees.includes(searchText);
            var matchesFilter = filterType === 'all' || itemType === filterType;

            if (matchesSearch && matchesFilter) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    };

    $scope.saveMappings = function() {
        $scope.goToReview();
    };

    // Final confirmation function
    $scope.confirmMappings = function() {
        if ($scope.mappings.length === 0) {
            toastService.error('No mappings to save.');
            return;
        }

        $rootScope.showFullScreenLoader = true;
        var savedCount = 0;
        var totalMappings = 0;
        var errors = [];

        // Calculate total number of individual mappings to save
        $scope.mappings.forEach(function(mapping) {
            if (mapping.applyTo === 'all') {
                totalMappings += mapping.employees.length;
            } else {
                totalMappings += 1; // Individual mapping
            }
        });

        console.log('Total mappings to save:', totalMappings);

        // Process each mapping
        $scope.mappings.forEach(function(mapping) {
            if (mapping.applyTo === 'all') {
                // Save mapping for each selected employee
                mapping.employees.forEach(function(employee) {
                    $scope.saveSingleMappingWithValidation(mapping, employee, function(success, error) {
                        savedCount++;
                        if (error) {
                            errors.push(error);
                        }
                        $scope.checkSaveCompletion(savedCount, totalMappings, errors);
                    });
                });
            } else {
                // Individual mapping - would need to implement individual employee selection
                // For now, we'll skip individual mappings or apply to first selected employee
                if ($scope.selectedEmployees.length > 0) {
                    $scope.saveSingleMappingWithValidation(mapping, $scope.selectedEmployees[0], function(success, error) {
                        savedCount++;
                        if (error) {
                            errors.push(error);
                        }
                        $scope.checkSaveCompletion(savedCount, totalMappings, errors);
                    });
                }
            }
        });
    };

    // Save a single mapping for one employee
    $scope.saveSingleMapping = function(mapping, employee, callback) {
        var requestData = {
            eligibilityType: mapping.eligibilityType.toUpperCase(), // ATTENDANCE or APPROVAL
            empId: employee.employeeId || employee.id.toString(),
            mappingType: mapping.type.toUpperCase(), // UNIT, CITY, or REGION
            value: mapping.value.toString()
        };

        console.log('Saving mapping:', requestData);

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.employeeAttendanceMapping,
            data: requestData,
            headers: {
                'Content-Type': 'application/json',
                'X-User-Id': $rootScope.currentUser ? $rootScope.currentUser.id : 'SYSTEM'
            }
        }).then(function success(response) {
            if(response.status === 500){
                toastService.error('Internal Server Error!');
                $rootScope.showFullScreenLoader = false;
            } else {
                console.log('Mapping saved successfully:', response.data);
                callback(true, null);
            }
        }, function error(response) {
            var errorMsg = 'Failed to save mapping for employee ' + (employee.name || employee.employeeId) +
                          ' (' + mapping.type + ': ' + mapping.name + ')';
            if (response.data && response.data.message) {
                errorMsg += ': ' + response.data.message;
            }
            callback(false, errorMsg);
        });
    };

    // Check if all mappings have been processed
    $scope.checkSaveCompletion = function(savedCount, totalMappings, errors) {
        if (savedCount >= totalMappings) {
            $rootScope.showFullScreenLoader = false;

            if (errors.length === 0) {
                toastService.success('All mappings saved successfully! Total: ' + totalMappings);
                // Reset form and go back to step 1
                $scope.resetForm();
            } else if (errors.length < totalMappings) {
                var successCount = totalMappings - errors.length;
                toastService.warning('Partially saved: ' + successCount + ' successful, ' + errors.length + ' failed.');
                console.error('Save errors:', errors);
                // Show detailed errors
                $scope.showSaveErrors(errors);
            } else {
                toastService.error('Failed to save all mappings. Please try again.');
                console.error('All save operations failed:', errors);
                $scope.showSaveErrors(errors);
            }
        }
    };

    // Show detailed save errors
    $scope.showSaveErrors = function(errors) {
        var errorMessage = 'The following errors occurred:\n\n';
        errors.forEach(function(error, index) {
            errorMessage += (index + 1) + '. ' + error + '\n';
        });
        alert(errorMessage);
    };

    // Reset form after successful save
    $scope.resetForm = function() {
        $scope.selectedEmployees = [];
        $scope.mappings = [];
        $scope.currentStep = 1;
        $scope.eligibilityType = 'attendance';
        $scope.selectedMappingType = 'unit';
        $scope.applyTo = 'all'; // Reset apply to dropdown
        $scope.updateStepIndicator();
        $scope.updateMappingsList();

        // Clear filters
        $scope.clearUnitFilters();
        $scope.cityMappingValue = '';
        $scope.regionMappingValue = '';

        // Reset search and pagination
        $scope.searchText = '';
        $scope.currentPage = 1;
        $scope.filteredEmployees = [];
        $scope.paginatedEmployees = [];

        // Scroll to top
        $timeout(function() {
            window.scrollTo(0, 0);
        });
    };

    $scope.getEmployeeNames = function(employees) {
        if (!employees || employees.length === 0) {
            return 'No employees';
        }
        return employees.map(function(emp) {
            return emp.name || emp.firstName + ' ' + emp.lastName;
        }).join(', ');
    };

    // Format mapping display as Employee -> Unit/City/Region
    $scope.formatMappingDisplay = function(mapping) {
        var employeeNames = $scope.getEmployeeNames(mapping.employees);
        var mappingTypeLabel = mapping.type.charAt(0).toUpperCase() + mapping.type.slice(1);

        if (mapping.employees.length === 1) {
            return employeeNames + ' → ' + mappingTypeLabel + ': ' + mapping.name;
        } else if (mapping.employees.length > 1) {
            return mapping.employees.length + ' Employees → ' + mappingTypeLabel + ': ' + mapping.name;
        } else {
            return 'No Employees → ' + mappingTypeLabel + ': ' + mapping.name;
        }
    };

    // Get mapping summary for review
    $scope.getMappingSummary = function(mapping) {
        var summary = {
            type: mapping.type,
            value: mapping.name,
            employeeCount: mapping.employees.length,
            employees: mapping.employees
        };
        return summary;
    };

    // Edit mapping function (not working)
    $scope.editMapping = function(index) {
        // Implementation for editing mappings
        console.log('Edit mapping at index:', index);
    };



    // Searchable Dropdown Functions
    $scope.filterDesignations = function() {
        $scope.filteredDesignations = $scope.designations.filter(function(designation) {
            var designationName = designation.name || designation.designationName || designation;
            return !$scope.designationSearch || designationName.toLowerCase().includes($scope.designationSearch.toLowerCase());
        });
    };

    $scope.filterDepartments = function() {
        $scope.filteredDepartments = $scope.departments.filter(function(department) {
            var departmentName = department.name || department.departmentName || department;
            return !$scope.departmentSearch || departmentName.toLowerCase().includes($scope.departmentSearch.toLowerCase());
        });
    };

    $scope.filterCategories = function() {
        $scope.filteredCategories = $scope.categories.filter(function(category) {
            return !$scope.categorySearch || category.name.toLowerCase().includes($scope.categorySearch.toLowerCase());
        });
    };

    $scope.filterUnitZones = function() {
        $scope.filteredUnitZones = $scope.unitZones.filter(function(unitZone) {
            return !$scope.unitZoneSearch || unitZone.name.toLowerCase().includes($scope.unitZoneSearch.toLowerCase());
        });
    };

    $scope.filterCities = function () {
        var query = ($scope.citySearch || "").toLowerCase();
        $scope.filteredCities = $scope.cityList.filter(function (city) {
            return city.name && city.name.toLowerCase().includes(query);
        });
    };


    $scope.filterRegions = function() {
        $scope.filteredRegions = $scope.regions.filter(function(region) {
            var regionName = region.name || region;
            return !$scope.regionSearch || regionName.toLowerCase().includes($scope.regionSearch.toLowerCase());
        });
    };

    $scope.filterUnitsDropdown = function() {
        $scope.filteredUnitsDropdown = $scope.filteredUnits.filter(function(unit) {
            return !$scope.unitSearch ||
                   unit.name.toLowerCase().includes($scope.unitSearch.toLowerCase()) ||
                   unit.id.toString().includes($scope.unitSearch);
        });
    };

    // Dropdown selection functions
    $scope.selectDesignation = function(designation) {
        var designationName = designation.name || designation.designationName || designation;
        $scope.designationSearch = designationName;
        $scope.filters.designation = designationName;
        $scope.showDesignationDropdown = false;
    };

    $scope.selectDepartment = function(department) {
        var departmentName = department.name || department.departmentName || department;
        $scope.departmentSearch = departmentName;
        $scope.filters.department = departmentName;
        $scope.showDepartmentDropdown = false;
    };

    $scope.selectCategory = function(category) {
        $scope.categorySearch = category.name;
        $scope.unitFilters.category = category.value;
        $scope.showCategoryDropdown = false;
        $scope.onUnitFilterChange();
    };

    $scope.selectUnitZone = function(unitZone) {
        $scope.unitZoneSearch = unitZone.name;
        $scope.unitFilters.unitZone = unitZone.value;
        $scope.showUnitZoneDropdown = false;
        $scope.onUnitFilterChange();
    };

    $scope.selectCity = function(city) {
        $scope.citySearch = city.name || city;
        $scope.unitFilters.city = city.name || city;
        $scope.showCityDropdown = false;
        $scope.onUnitFilterChange();
    };

    // City mapping selection
    $scope.selectCityMapping = function(city) {
        $scope.cityMappingValue = city.name || city;
    };

    // Region mapping selection
    $scope.selectRegionMapping = function(region) {
        $scope.regionMappingValue = region.name || region;
    };

    $scope.selectRegion = function(region) {
        $scope.regionSearch = region.name || region;
        $scope.unitFilters.region = region.name || region;
        $scope.showRegionDropdown = false;
        $scope.onUnitFilterChange();
    };

    $scope.selectUnit = function(unit) {
        $scope.unitSearch = unit.id + ' - ' + unit.name;
        $scope.unitFilters.selectedUnitId = unit.id;
        $scope.unitFilters.selectedUnitName = unit.name;
        $scope.showUnitDropdown = false;
    };

    // Dropdown hide functions with delay
    $scope.hideDesignationDropdown = function() {
        $timeout(function() {
            $scope.showDesignationDropdown = false;
        }, 200);
    };

    $scope.hideDepartmentDropdown = function() {
        $timeout(function() {
            $scope.showDepartmentDropdown = false;
        }, 200);
    };

    $scope.hideCategoryDropdown = function() {
        $timeout(function() {
            $scope.showCategoryDropdown = false;
        }, 200);
    };

    $scope.hideUnitZoneDropdown = function() {
        $timeout(function() {
            $scope.showUnitZoneDropdown = false;
        }, 200);
    };

    $scope.hideCityDropdown = function() {
        $timeout(function() {
            $scope.showCityDropdown = false;
        }, 200);
    };

    $scope.hideRegionDropdown = function() {
        $timeout(function() {
            $scope.showRegionDropdown = false;
        }, 200);
    };

    $scope.hideUnitDropdown = function() {
        $timeout(function() {
            $scope.showUnitDropdown = false;
        }, 200);
    };

    $scope.hideCityMappingDropdown = function() {
        $timeout(function() {
            $scope.showCityMappingDropdown = false;
        }, 200);
    };

    $scope.hideRegionMappingDropdown = function() {
        $timeout(function() {
            $scope.showRegionMappingDropdown = false;
        }, 200);
    };

    // Initialize filtered arrays when data loads
    $scope.$watch('designations', function(newVal) {
        if (newVal) {
            $scope.filteredDesignations = newVal.slice();
        }
    });

    $scope.$watch('departments', function(newVal) {
        if (newVal) {
            $scope.filteredDepartments = newVal.slice();
        }
    });

    $scope.$watch('categories', function(newVal) {
        if (newVal) {
            $scope.filteredCategories = newVal.slice();
        }
    });

    $scope.$watch('unitZones', function(newVal) {
        if (newVal) {
            $scope.filteredUnitZones = newVal.slice();
        }
    });

    $scope.$watch('cities', function(newVal) {
        if (newVal) {
            $scope.filteredCities = newVal.slice();
        }
    });

    $scope.$watch('regions', function(newVal) {
        if (newVal) {
            $scope.filteredRegions = newVal.slice();
        }
    });

    $scope.$watch('filteredUnits', function(newVal) {
        if (newVal) {
            $scope.filteredUnitsDropdown = newVal.slice();
        }
    });

    // Update pagination when filtered employees change
    $scope.$watch('filteredEmployees', function(newVal) {
        if (newVal) {
            $scope.updatePagination();
        }
    });
    // Validate mapping data before sending
    $scope.validateMappingData = function(mapping, employee) {
        var errors = [];

        if (!mapping.eligibilityType) {
            errors.push('Eligibility type is required');
        }

        if (!employee.employeeId && !employee.id) {
            errors.push('Employee ID is required');
        }

        if (!mapping.type) {
            errors.push('Mapping type is required');
        }

        if (!mapping.value) {
            errors.push('Mapping value is required');
        }

        return errors;
    };

    // Enhanced save function with validation
    $scope.saveSingleMappingWithValidation = function(mapping, employee, callback) {
        // Validate data first
        var validationErrors = $scope.validateMappingData(mapping, employee);
        if (validationErrors.length > 0) {
            var errorMsg = 'Validation failed for employee ' + (employee.name || employee.employeeId) + ': ' + validationErrors.join(', ');
            callback(false, errorMsg);
            return;
        }
        // Proceed with save
        $scope.saveSingleMapping(mapping, employee, callback);
    };

    $scope.exportAllEmployeeMappings = function () {
        const empIds = $scope.selectedEmployees.map(function (emp) {
            return emp.employeeId || emp.id;
        }).filter(Boolean);

        if (!empIds.length) {
            toastService.error("Please select employees before exporting.");
            return;
        }

        console.log("Exporting for employees:", empIds);

        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.exportMappingsExcel,
            data: empIds,
            responseType: 'arraybuffer'
        }).then(function (response) {
            var blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            if (response.status === 500) {
                toastService.error("Nothing mapped for selected employees.");
                return;
            } else if (response.status === 204 || response.status === 404) {
                toastService.error("No data available to export.");
                return;
            }
            saveAs(blob, "All_Employee_Mappings_" + new Date().toISOString().slice(0, 10) + ".xlsx");
        }, function (err) {
            toastService.error("Export failed.");
            console.error("Export error:", err);
        });
    };

    // Task 1: City and Region mapping filter functions
    $scope.filterCitiesMapping = function() {
        $scope.filteredCitiesMapping = $scope.cityList.filter(function(city) {
            var cityName = city.name || city;
            return !$scope.cityMappingSearch || cityName.toLowerCase().includes($scope.cityMappingSearch.toLowerCase());
        });
    };

    $scope.filterRegionsMapping = function() {
        $scope.filteredRegionsMapping = $scope.regionList.filter(function(region) {
            var regionName = region.name || region;
            return !$scope.regionMappingSearch || regionName.toLowerCase().includes($scope.regionMappingSearch.toLowerCase());
        });
    };

    $scope.selectCityMapping = function(city) {
        var cityName = city.name || city;
        $scope.cityMappingSearch = cityName;
        $scope.cityMappingValue = cityName;
        $scope.showCityMappingDropdown = false;
    };

    $scope.selectRegionMapping = function(region) {
        var regionName = region.name || region;
        $scope.regionMappingSearch = regionName;
        $scope.regionMappingValue = regionName;
        $scope.showRegionMappingDropdown = false;
    };

    // Task 2: Employee mapping search functionality
    $scope.filterEmployeeMappings = function() {
        if (!$scope.employeeMappingSearch) {
            $scope.filteredEmployeeMappings = $scope.employeeMappings.slice();
            return;
        }

        var searchTerm = $scope.employeeMappingSearch.toLowerCase();
        $scope.filteredEmployeeMappings = $scope.employeeMappings.filter(function(mapping) {
            return (mapping.mappingType && mapping.mappingType.toLowerCase().includes(searchTerm)) ||
                   (mapping.eligibilityType && mapping.eligibilityType.toLowerCase().includes(searchTerm)) ||
                   (mapping.displayValue && mapping.displayValue.toLowerCase().includes(searchTerm)) ||
                   (mapping.status && mapping.status.toLowerCase().includes(searchTerm)) ||
                   (mapping.createdBy && mapping.createdBy.toLowerCase().includes(searchTerm));
        });
    };

    // Task 3: Download template functionality (from backend)
    $scope.downloadTemplate = function() {
        $rootScope.showFullScreenLoader = true;

        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.downloadTemplate,
            responseType: 'arraybuffer'
        }).then(function success(response) {
            var blob = new Blob([response.data], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            var fileName = 'Employee_Eligibility_Mapping_Template_' + new Date().toISOString().slice(0, 10) + '.xlsx';
            saveAs(blob, fileName);
            toastService.success('Template downloaded successfully!');
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            console.error('Error downloading template:', response);
            toastService.error('Failed to download template. Please try again.');
            $rootScope.showFullScreenLoader = false;
        });
    };

    // Task 4: Bulk upload functionality
    $scope.triggerBulkUpload = function() {
        document.getElementById('bulkUploadFile').click();
    };

    $scope.handleBulkUpload = function(event) {
        var file = event.target.files[0];
        if (!file) return;

        $scope.processingBulkUpload = true;
        $scope.showBulkUploadModal = true;
        $scope.bulkUploadData = [];
        $scope.filteredBulkUploadData = [];
        $scope.bulkUploadFilter = 'all';

        var reader = new FileReader();
        reader.onload = function(e) {
            try {
                var data = new Uint8Array(e.target.result);
                var workbook = XLSX.read(data, {type: 'array'});
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];
                var jsonData = XLSX.utils.sheet_to_json(worksheet);

                $scope.processBulkUploadData(jsonData);
            } catch (error) {
                console.error('Error reading file:', error);
                toastService.error('Error reading file. Please check the file format.');
                $scope.processingBulkUpload = false;
                $scope.showBulkUploadModal = false;
            }
        };
        reader.readAsArrayBuffer(file);

        // Reset file input
        event.target.value = '';
    };

    $scope.processBulkUploadData = function(jsonData) {
        // Convert Excel data to API request format
        var bulkData = [];

        jsonData.forEach(function(row, index) {
            var eligibilityType = row['Eligibility_type'] || row['eligibility_type'] || '';
            var empId = row['Emp_id'] || row['emp_id'] || '';
            var mappingType = row['Mapping_type'] || row['mapping_type'] || '';
            var value = row['value'] || row['Value'] || '';

            // Only add non-empty records
            if (eligibilityType || empId || mappingType || value) {
                var record = {
                    eligibilityType: eligibilityType.toUpperCase(),
                    empId: empId,
                    mappingType: mappingType.toUpperCase(),
                    value: value
                };
                bulkData.push(record);
            }
        });

        if (bulkData.length === 0) {
            toastService.error('No valid data found in the uploaded file.');
            $scope.processingBulkUpload = false;
            $scope.showBulkUploadModal = false;
            $scope.$apply();
            return;
        }

        // Store original data for later processing
        $scope.originalBulkData = bulkData;

        // Send data to backend for validation only
        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.validateBulkUpload,
            data: bulkData,
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(function success(response) {
            var validationResponse = response.data;

            // Convert backend response to frontend format for display
            $scope.bulkUploadData = [];

            // Create display records from original data with validation results
            bulkData.forEach(function(originalRecord, index) {
                var rowNumber = index + 2;
                var hasError = false;
                var errorMessages = [];

                // Find validation errors for this row
                if (validationResponse.validationErrors) {
                    validationResponse.validationErrors.forEach(function(error) {
                        if (error.rowNumber === rowNumber) {
                            hasError = true;
                            errorMessages.push(error.errorMessage);
                        }
                    });
                }

                $scope.bulkUploadData.push({
                    rowNumber: rowNumber,
                    eligibilityType: originalRecord.eligibilityType,
                    empId: originalRecord.empId,
                    mappingType: originalRecord.mappingType,
                    status: 'ACTIVE',
                    value: originalRecord.value,
                    isValid: !hasError,
                    validationError: errorMessages.join(', ')
                });
            });

            $scope.bulkUploadResponse = validationResponse;
            $scope.filterBulkUploadData();
            $scope.processingBulkUpload = false;

            // Show validation message
            if (validationResponse.success) {
                toastService.success('Validation completed successfully! Ready to upload.');
            } else {
                toastService.warning(validationResponse.message);
            }

            $scope.$apply();
        }, function error(response) {
            console.error('Error validating bulk upload:', response);
            toastService.error('Error validating bulk upload. Please check the console for details.');
            $scope.processingBulkUpload = false;
            $scope.showBulkUploadModal = false;
            $scope.$apply();
        });
    };

    $scope.filterBulkUploadData = function() {
        if ($scope.bulkUploadFilter === 'valid') {
            $scope.filteredBulkUploadData = $scope.bulkUploadData.filter(function(record) {
                return record.isValid;
            });
        } else if ($scope.bulkUploadFilter === 'invalid') {
            $scope.filteredBulkUploadData = $scope.bulkUploadData.filter(function(record) {
                return !record.isValid;
            });
        } else {
            $scope.filteredBulkUploadData = $scope.bulkUploadData.slice();
        }
    };

    $scope.getValidRecordsCount = function() {
        if ($scope.bulkUploadResponse) {
            return $scope.bulkUploadResponse.validRecords || 0;
        }
        return $scope.bulkUploadData.filter(function(record) {
            return record.isValid;
        }).length;
    };

    $scope.getInvalidRecordsCount = function() {
        if ($scope.bulkUploadResponse) {
            return $scope.bulkUploadResponse.invalidRecords || 0;
        }
        return $scope.bulkUploadData.filter(function(record) {
            return !record.isValid;
        }).length;
    };

    $scope.closeBulkUploadModal = function() {
        $scope.showBulkUploadModal = false;
        $scope.bulkUploadData = [];
        $scope.filteredBulkUploadData = [];
        $scope.bulkUploadResponse = null;
        $scope.originalBulkData = [];
        $scope.employeeMappingSearch = '';
    };

    $scope.confirmBulkUpload = function() {
        if (!$scope.originalBulkData || $scope.originalBulkData.length === 0) {
            toastService.error('No data to process');
            return;
        }

        // Filter only valid records for processing
        var validRecords = [];
        $scope.originalBulkData.forEach(function(record, index) {
            var rowNumber = index + 2;
            var hasError = false;

            // Check if this record has validation errors
            if ($scope.bulkUploadResponse && $scope.bulkUploadResponse.validationErrors) {
                $scope.bulkUploadResponse.validationErrors.forEach(function(error) {
                    if (error.rowNumber === rowNumber) {
                        hasError = true;
                    }
                });
            }

            if (!hasError) {
                validRecords.push(record);
            }
        });

        if (validRecords.length === 0) {
            toastService.error('No valid records to process');
            return;
        }

        $rootScope.showFullScreenLoader = true;

        // Send valid records to backend for actual processing
        $http({
            method: 'POST',
            url: AppUtil.restUrls.userManagement.bulkUpload,
            data: validRecords,
            headers: {
                'Content-Type': 'application/json',
                'X-User-Id': $rootScope.currentUser ? $rootScope.currentUser.id : 'SYSTEM'
            }
        }).then(function success(response) {
            var processResponse = response.data;
            $rootScope.showFullScreenLoader = false;

            if (processResponse.success) {
                toastService.success('Successfully processed ' + processResponse.successfullyProcessed + ' records!');
            } else {
                toastService.warning(processResponse.message);
            }

            $scope.closeBulkUploadModal();
        }, function error(response) {
            $rootScope.showFullScreenLoader = false;
            console.error('Error processing bulk upload:', response);
            toastService.error('Error processing bulk upload. Please check the console for details.');
        });
    };

    // Initialize filtered arrays when data loads
    $scope.$watch('cityList', function(newVal) {
        if (newVal) {
            $scope.filteredCities = newVal.slice();
            $scope.filteredCitiesMapping = newVal.slice();
        }
    });

    $scope.$watch('regionList', function(newVal) {
        if (newVal) {
            $scope.filteredRegions = newVal.slice();
            $scope.filteredRegionsMapping = newVal.slice();
        }
    });

//    // Function to get mapping count for an employee
//    $scope.getEmployeeMappingCount = function(employee) {
//        if (!employee || !employee.id) {
//            return 0;
//        }
//
//        // Check if we have cached mapping counts
//        if (!$scope.employeeMappingCounts) {
//            $scope.employeeMappingCounts = {};
//        }
//
//        // Return cached count if available
//        if ($scope.employeeMappingCounts[employee.id] !== undefined) {
//            return $scope.employeeMappingCounts[employee.id];
//        }
//
//        // Load mapping count asynchronously
//        $scope.loadEmployeeMappingCount(employee.id);
//        return 0; // Return 0 while loading
//    };
//
//    // Function to load mapping count for a specific employee
//    $scope.loadEmployeeMappingCount = function(empId) {
//        $http({
//            method: 'GET',
//            url: AppUtil.restUrls.userManagement.employeeAttendanceMapping + '/' + empId
//        }).then(function success(response) {
//            var mappings = response.data || [];
//            $scope.employeeMappingCounts[empId] = mappings.length;
//        }, function error(response) {
//            console.log('Error loading mapping count for employee:', empId, response);
//            $scope.employeeMappingCounts[empId] = 0;
//        });
//    };
//
//    // Initialize mapping counts cache
//    $scope.employeeMappingCounts = {};
//
//    // Function to load mapping counts for multiple employees at once
//    $scope.loadMappingCountsForEmployees = function(employees) {
//        if (!employees || employees.length === 0) return;
//
//        // Load counts for employees that don't have cached counts
//        employees.forEach(function(employee) {
//            if (employee.id && $scope.employeeMappingCounts[employee.id] === undefined) {
//                $scope.loadEmployeeMappingCount(employee.id);
//            }
//        });
//    };

    // Initialize the controller
    $scope.init();
});

// File upload directive for bulk upload
adminapp.directive('fileUpload', function() {
    return {
        restrict: 'A',
        link: function(scope, element, attrs) {
            element.bind('change', function(event) {
                scope.$apply(function() {
                    scope.$eval(attrs.fileUpload, {$event: event});
                });
            });
        }
    };
});
