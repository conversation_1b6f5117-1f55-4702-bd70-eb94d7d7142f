/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 29-04-2016.
 */
// import "./service/ConfigService"
function getCookie(name) {
    var cookie = {};
    document.cookie.split(';').forEach(function (el) {
        var result = el.split('=');
        var k = result[0];
        var v = result[1];
        cookie[k.trim()] = v;
    })
    return cookie[name];
}
(function () {
    'use strict';
    angular.module('adminapp').factory('APIJson', APIJson);
    APIJson.$inject = ['ConfigService'];
    function getAppCacheBaseUrl() {
        if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
            return "https://app.chaayos.com/app-cache/";
        } else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
            return "https://relax.chaayos.com/";
        } else {
            return "http://dev.kettle.chaayos.com:8081/app-cache/";
        }
    }

    function getDataHubUrl(){
       if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
                return "https://internal.chaayos.com/";
            }else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
                return "https://relax.chaayos.com/";
            } else if (window.location.origin.indexOf("stage") > -1) {
                return "http://stage.kettle.chaayos.com:8081/"
            } else if (window.location.origin.indexOf("dev") > -1) {
                return "http://dev.kettle.chaayos.com:8081/"
            } else if (window.location.origin.indexOf("localhost") > -1) {
                return "http://localhost:8080/"
            }
    }

    function getCrmServiceUrl(){
        if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
            return "https://internal.chaayos.com/";
        }else if (window.location.origin.indexOf("relax.chaayos.com") >= 0) {
            return "https://relax.chaayos.com/";
        }else if (window.location.origin.indexOf("stage") > -1) {
            return "http://stage.kettle.chaayos.com:8081/"
        } else if (window.location.origin.indexOf("dev") > -1) {
            return "http://dev.kettle.chaayos.com:8081/"
        } else if (window.location.origin.indexOf("localhost") > -1) {
            return "http://localhost:8080/"
        }
    }

    function getAppCrmBaseUrl() {
        if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
            return "https://app.chaayos.com/app-crm/";
        } else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
            return "https://relax.chaayos.com/";
        }else {
            return "http://**************:8989/app-crm/";
        }
    }

    function getNeoBaseUrl() {
        if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
            return "https://internal.chaayos.com/";
        } else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
            return "https://relax.chaayos.com/";
        }else if (window.location.origin.indexOf("stage") > -1) {
            return "http://stage.kettle.chaayos.com:8080/"
        } else if (window.location.origin.indexOf("dev") > -1) {
            return "http://dev.kettle.chaayos.com:8080/"
        } else if (window.location.origin.indexOf("localhost") > -1) {
            return "http://localhost:8080/"
        }
    }

    function getB2BMonkCrmBaseUrl() {
        if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
            return "https://app.chaayos.com/";
        }else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
            return "https://relax.chaayos.com/";
        }else {
            return "http://**************:7878/";
        }
    }

    function getB2BMonkServiceBaseUrl() {
        if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
            return "https://app.chaayos.com/";
        }else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
            return "https://relax.chaayos.com/";
        }else {
            return "http://**************:8599/";
        }
    }

    function getCrmDroolUrl(){
        if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
                    return "https://internal.chaayos.com/crm-service/rest/v2/";
        }else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
                     return "https://relax.chaayos.com/";
        } else if (window.location.origin.indexOf("stage") > -1) {
                    return "http://stage.kettle.chaayos.com:8081/crm-service/rest/v2/"
        } else if (window.location.origin.indexOf("dev") > -1) {
                    return "http://dev.kettle.chaayos.com:8081/crm-service/rest/v2/"
        } else if (window.location.origin.indexOf("localhost") > -1) {
                    return "http://localhost:8085/crm-service/rest/v2/"
        }
    }

    function getTransactionUrl(){
            if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
                        return "https://internal.chaayos.com/transaction-service/rest/v2/";
            }else if (window.location.origin.indexOf("relax.chaayos.com") >= 0) {
                        return "https://relax.chaayos.com/transaction-service/rest/v2/";
            }else if (window.location.origin.indexOf("stage") > -1) {
                        return "http://stage.kettle.chaayos.com:8081/transaction-service/rest/v2/"
            } else if (window.location.origin.indexOf("dev") > -1) {
                        return "http://dev.kettle.chaayos.com:8081/transaction-service/rest/v2/"
            } else if (window.location.origin.indexOf("localhost") > -1) {
                        return "http://localhost:8085/transaction-service/rest/v2/"
            }
        }

    function APIJson(ConfigService) {
        var EnvName = getCookie("EnvName");
        var baseUrl = "http://stage.kettle.chaayos.com:8080/";
        var scmBaseUrl = ConfigService.getUrls(EnvName).scmBaseUrl;
        var neoBaseUrl = getNeoBaseUrl();
        var appCache = getAppCacheBaseUrl();
        var appCrm = getAppCrmBaseUrl();
        var dataHubUrl = getDataHubUrl();
        var crmServiceUrl=getCrmServiceUrl();
        var transactionServiceUrl = getTransactionUrl();
        // var neoBaseUrl = "http://localhost:8080/";
        // var appCache = "http://localhost:8787/app-cache/";
        // var appCrm = "http://localhost:8989/app-crm/";
        var channelPartnerUrl = window.location.origin + "/";
        var b2bMonkCrmBaseUrl = getB2BMonkCrmBaseUrl();
        var b2bMonkServiceBaseUrl = getB2BMonkServiceBaseUrl();
        var crmDrool = getCrmDroolUrl();

        var KETTLE_CONTEXT = baseUrl + "kettle-service/rest/v1/";
        var MASTER_CONTEXT = "http://localhost:9696/" + "master-service/rest/v1/";
        var OFFER_CONTEXT = baseUrl + "offer-service/rest/v1/";
        var REPORT_CONTEXT = baseUrl + "report-service/rest/v1/";
        var NEO_CONTEXT = neoBaseUrl + "neo-service/rest/v1/";
        var CUSTOMER_CONTEXT = baseUrl + "kettle-crm/rest/v1/";
        var ANALYTICS_CONTEXT = baseUrl + "kettle-analytics/rest/v1/";
        var SCM_SERVICE = scmBaseUrl + "scm-service/rest/v1/";
        var CHECKLIST_CONTEXT = baseUrl + "kettle-checklist/rest/v1/";
        var TRANSACTION_SERVICE_CONTEXT = transactionServiceUrl;
        var KETTLE_SERVICE_CONTEXT = baseUrl + "kettle-service/rest/v1/"
        var DATA_HUB_SERVICE_CONTEXT = dataHubUrl + "datahub/rest/v1/";
        var CRM_SERVICE_CONTEXT=crmServiceUrl+"crm-service/rest/v2/";

        var CHANNEL_PARTNER_SERVICE_CONTEXT = channelPartnerUrl + "channel-partner/rest/v1/";
        var DINE_IN_APP_CACHE_CONTEXT = appCache + "cache";
        var DINE_IN_APP_LOCATION_CONTEXT = appCache + "location";
        var DINE_IN_APP_CUSTOMER_OONTEXT = appCrm + "v2/crm";
        var DINE_IN_APP_APP_SECTION = appCache + "unit";
        var NOTIFICATION_CONTEXT = appCrm + "v2/";
        var KETTLE_JOB = baseUrl + "kettle-jobs/rest-jobs/v1/";
        var REKOG_SERVICE = baseUrl + "rekognition-service/rest/v1/rekog/"
        var B2B_MONK_CRM = b2bMonkCrmBaseUrl + "monk-crm/";
        var B2B_MONK_SERVICE = b2bMonkServiceBaseUrl + "monk-service/";

        var POS_METADATA_ROOT_CONTEXT = KETTLE_CONTEXT + "pos-metadata";
        var CASHBACK_OFFER_ROOT_CONTEXT = KETTLE_CONTEXT + "cashbk-offer-resource";
        var RECIPE_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT + "recipe";
        var SCM_RECIPE_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT + "scm-recipe";
        var USER_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT + "users";
        var UNIT_METADATA_ROOT_CONTEXT = MASTER_CONTEXT + "unit-metadata";
        var UNIT_DROOL_METADATA_ROOT_CONTEXT = MASTER_CONTEXT + "unit-drool-management";
        var CRM_SERVICES_ROOT_CONTEXT = CUSTOMER_CONTEXT + "crm";
        var CUSTOMER_SERVICES_ROOT_CONTEXT = CUSTOMER_CONTEXT + "customer";
        var KIOSK_ROOT_CONTEXT = MASTER_CONTEXT + "kiosk-management/";
        var PRODUCT_METADATA_ROOT_CONTEXT = MASTER_CONTEXT + "product-metadata";
        var GENERIC_EXCEL_ROOT_CONTEXT = MASTER_CONTEXT + "generic-excel-management";
        var PRICE_PROFILE_METADATA_ROOT_CONTEXT = MASTER_CONTEXT + "price-profile";
        var BRAND_METADATA_ROOT_CONTEXT = MASTER_CONTEXT + "brand-metadata";
        var ALIAS_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT + "alias-management";
        var REPORT_SERVICE_METADATA_ROOT_CONTEXT = REPORT_CONTEXT + "report-metadata";
        var REPORT_METADATA_ROOT_CONTEXT = KETTLE_CONTEXT + "report-metadata";
        var CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT = CUSTOMER_CONTEXT + "customer-profile";
        var REFERRAL_SERVICES_ROOT_CONTEXT = CUSTOMER_CONTEXT + "ref";
        var OFFER_MANAGEMENT_ROOT_CONTEXT = OFFER_CONTEXT + "offer-management";
        var USER_MANAGEMENT_SERVICES_ROOT_CONTEXT = MASTER_CONTEXT + "user-management";
        var CASH_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "cash-management";
        var SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "product-management/";
        var SCM_PRICE_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "price-management/";
        var SCM_METADATA_ROOT_CONTEXT = SCM_SERVICE + "scm-metadata/";
        var SCM_UNIT_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "unit-management/";
        var CHEKLIST_METADATA_CONTEXT = CHECKLIST_CONTEXT + "checklist-metadata/";
        var CHEKLIST_SERVICE_CONTEXT = CHECKLIST_CONTEXT + "checklist-service/";
        var CHEKLIST_MANAGEMENT_CONTEXT = CHECKLIST_CONTEXT + "checklist-management/";
        var CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "customer-offer-management";
        var KETTLE_OFFER_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "offer-management";
        var ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT + "access-control-management/";
        var UNIT_METADATA_MANAGMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "metadata-management/";
        var MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT = "http://stage.kettle.chaayos.com:8080/master-service/rest/v1/" + "master-cache-management/";
        var SESSION_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT + "session-cache-management/";
        var TAX_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT + "tax-metadata/";
        var GIFT_CARD_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "gift-card-management/";
        var BUDGET_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "budget-metadata/";
        var APPS_MANAGEMENT_ROOT_CONTEXT = ANALYTICS_CONTEXT + "app-management/";
        var ANALYTICS_ROOT_CONTEXT = ANALYTICS_CONTEXT + "analytics/";
        var PARTNER_METADATA_MANAGEMENT_CONTEXT = CHANNEL_PARTNER_SERVICE_CONTEXT + "partner-metadata-management/";
        var PARTNER_MANAGEMENT_CONTEXT = CHANNEL_PARTNER_SERVICE_CONTEXT + "partner-management/";
        var PARTNER_CACHE_MANAGEMENT_CONTEXT = CHANNEL_PARTNER_SERVICE_CONTEXT + "channel-partner-cache/";
        var PARTNER_REPORT_CONTEXT = CHANNEL_PARTNER_SERVICE_CONTEXT + "reports/";
        var APPLICATION_INSTALLATION_ROOT_CONTEXT = MASTER_CONTEXT + "application-installation/";
        var SCM_CACHE_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "cache-management/";
        var CHANNEL_PARTNER_ROOT_CONTEXT = MASTER_CONTEXT + "channel-partner";
        var EXPENSE_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "expense-management";
        var NOTIFICATION_ROOT_CONTEXT = NOTIFICATION_CONTEXT + "notify";
        var BANNER_ROOT_CONTEXT = OFFER_CONTEXT + "banner-management";
        var KETTLE_JOB_CONTEXT = KETTLE_CONTEXT + "event";
        var REFERENCE_MANAGEMENT_ROOT_CONTEXT = SCM_SERVICE + "reference-order-management";

        var MASTER_METADATA_ROOT_CONTEXT = MASTER_CONTEXT + "metadata/";
        var MASTER_OFFER_ROOT_CONTEXT = MASTER_CONTEXT + "offer-management/";
        var KETTLE_ORDER_ROOT_CONTEXT = KETTLE_CONTEXT + "order-management/"
        var PAYMENT_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT + "payment-management/";
        var PRICE_MANAGEMENT_ROOT_CONTEXT = OFFER_CONTEXT + "price-management";
        var RECIPE_MEDIA_MANAGEMENT_ROOT_CONTEXT = OFFER_CONTEXT + "recipe-media-management";
        var ORDER_MANAGEMENT_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "order-management" + "/";
        var PARTNER_ORDER_ROOT_CONTEXT = CHANNEL_PARTNER_SERVICE_CONTEXT + "partner-order/";
        var DROOL_MANAGEMENT_ROOT_CONTEXT = KETTLE_CONTEXT + "drool-resource/"
        var DROOL_CRM_MANAGEMENT_ROOT_CONTEXT = crmDrool + "drools-resource/"
        var DROOL_TRANSACTION_ROOT_CONTEXT = transactionServiceUrl + "drools-resource/"
        var B2B_MONK_USER_MANAGEMENT_CONTEXT = B2B_MONK_CRM + "user/";
        var B2B_MONK_ACTION_MANAGEMENT_CONTEXT = B2B_MONK_SERVICE + "action/";
        var DATA_ENCRYPTION_ROOT_CONTEXT = REPORT_CONTEXT + "crypto/";
        var TRANSACTION_METADATA_ROOT_CONTEXT = KETTLE_SERVICE_CONTEXT + "transaction-resource/";
        var STORE_MANAGEMENT_ROOT_CONTEXT = KETTLE_SERVICE_CONTEXT + "store-management/";
        var AB_TESTING_ROOT_CONTEXT = DATA_HUB_SERVICE_CONTEXT + "experiment-management/";
        var DATAHUB_REFRESH_CACHE_ROOT_CONTEXT = DATA_HUB_SERVICE_CONTEXT + "unit-metadata-refresh/";
        var DATAHUB_METADATA_CACHE_ROOT_CONTEXT = DATA_HUB_SERVICE_CONTEXT + "unit-metadata/";
        var RECOM_SERVICE_ROOT_CONTEXT= CRM_SERVICE_CONTEXT+"recom/";
        var REVENUE_CERTIFICATE = MASTER_CONTEXT + "report/";
        var ATTRIBUTE_MANAGEMENT_ROOT_CONTECT = SCM_SERVICE + "attribute-management";
        var EMPLOYEE_MANAGEMENT_ROOT_CONTEXT = MASTER_CONTEXT + "employee-management";
        var STORE_OPS_ROOT_CONTEXT = dataHubUrl + "store-ops/rest/v1/";
        var MONK_META_DATA = MASTER_CONTEXT + "monk-metadata";


        var service = {};

        service.urls = {
            appSection: {
                addSectionDetails: DINE_IN_APP_APP_SECTION + "/upsert/sequence",
                getSequence: DINE_IN_APP_APP_SECTION + "/sequence",
                setShuffle: DINE_IN_APP_APP_SECTION + "/update/category-sequence",
                getCategorySequence: DINE_IN_APP_APP_SECTION + "/category-sequence"
            },
            channelPartner: {
                getAllChannelPartners: CHANNEL_PARTNER_ROOT_CONTEXT + "/channel-partner",
                updateChannelPartner: CHANNEL_PARTNER_ROOT_CONTEXT + "/channel-partner/update",
                getActiveChannelPartners: CHANNEL_PARTNER_ROOT_CONTEXT + "/channel-partner/active",
                getPriceProfileStrategy: CHANNEL_PARTNER_ROOT_CONTEXT + "/get-price-profile-strategy",
                addPriceProfile: CHANNEL_PARTNER_ROOT_CONTEXT + "/add-price-profile-mapping",
                updatePriceProfile: CHANNEL_PARTNER_ROOT_CONTEXT + "/update-price-profile-mapping",
                getUnitPartnerMenuMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-menu/get",
                addUnitPartnerMenuMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-menu/add",
                addUnitPartnerMenuMappingBulk: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-menu/add-bulk",
                toggleUnitChannelPartnerMenuMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-menu/status-change-bulk",
                activateUnitPartnerMenuMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-menu/activate",
                deactivateUnitPartnerMenuMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-menu/deactivate",
                getActiveUnitChannelPartnerMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-channel-partner",
                getAllUnitChannelPartnerMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-channel-partner/get",
                addUnitChannelPartnerMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-channel-partner/add",
                activateUnitChannelPartnerMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-channel-partner/activate",
                deactivateUnitChannelPartnerMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-channel-partner/deactivate",
                createProductGroups: CHANNEL_PARTNER_ROOT_CONTEXT + "/groups/create",
                getProductGroups: CHANNEL_PARTNER_ROOT_CONTEXT + "/groups/get",
                getProductGroupsByFilter: CHANNEL_PARTNER_ROOT_CONTEXT + "/groups/get/menu-app",
                mapProductSequence: CHANNEL_PARTNER_ROOT_CONTEXT + "/groups/product/sequence/create",
                updateProductSequence: CHANNEL_PARTNER_ROOT_CONTEXT + "/group/product/sequence/update",
                getMenus: CHANNEL_PARTNER_ROOT_CONTEXT + "/menus/get",
                getMenusShort: CHANNEL_PARTNER_ROOT_CONTEXT + "/menus/get/short",
                getMenuBySequenceId: CHANNEL_PARTNER_ROOT_CONTEXT + "/menu/get",
                createMenu: CHANNEL_PARTNER_ROOT_CONTEXT + "/menu/create",
                createMenuSequence: CHANNEL_PARTNER_ROOT_CONTEXT + "/menu/sequence/create",
                getMenuSequence: CHANNEL_PARTNER_ROOT_CONTEXT + "/menu-sequence-get",
                uploadIcon: CHANNEL_PARTNER_ROOT_CONTEXT + "/upload-icon",
                uploadCategoryImage: CHANNEL_PARTNER_ROOT_CONTEXT + "/upload-category-image",
                allIcons: CHANNEL_PARTNER_ROOT_CONTEXT + "/icons",
                menuType: CHANNEL_PARTNER_ROOT_CONTEXT + "/menu-type",
                updateProductGroups: CHANNEL_PARTNER_ROOT_CONTEXT + "/groups/update",
                groupMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/getGroupMapping",
                getRecommendation: CHANNEL_PARTNER_ROOT_CONTEXT + "/recommendation/get",
                getPriceAllProfile: CHANNEL_PARTNER_ROOT_CONTEXT + "/get-all-profile-price",
                getPriceProfile: CHANNEL_PARTNER_ROOT_CONTEXT + "/get-price-profile",
                createRecommendation: CHANNEL_PARTNER_ROOT_CONTEXT + "/recommendation/create",
                createRecommendationMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/recommendation/mapping",
                updateRecommendationStatus: CHANNEL_PARTNER_ROOT_CONTEXT + "/recommendation/status",
                menuApp: CHANNEL_PARTNER_ROOT_CONTEXT + "/menu-app",
                recommendationUnitChannelPartnerMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-recommendation-mapping",
                priceProfileUnitChannelPartnerMapping: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-price-profile-mapping",
                unitIdsForMenuSequence: CHANNEL_PARTNER_ROOT_CONTEXT + "/unitIds/get",
                getDynamicPriceProfile: CHANNEL_PARTNER_ROOT_CONTEXT + "/unit-partner-menu/get-profile-price",
                changeMenuStatus: CHANNEL_PARTNER_ROOT_CONTEXT + "/change-menu-status"
            },
            posMetaData: {
                allDeliveryPartners: POS_METADATA_ROOT_CONTEXT + "/delivery-partners",
                creditAccountAll: POS_METADATA_ROOT_CONTEXT + "/creditAccount/all",
                creditAccountAdd: POS_METADATA_ROOT_CONTEXT + "/creditAccount/add",
                creditAccountUpdate: POS_METADATA_ROOT_CONTEXT + "/creditAccount/update",
                refLookUpUpsert: UNIT_METADATA_ROOT_CONTEXT + "/refLookUp/upsert",
                auditReport: POS_METADATA_ROOT_CONTEXT + "/audit/unit",
                packagingProfile: POS_METADATA_ROOT_CONTEXT + "/unit/packaging-profile",
                brandPartnerPackagingProfile: POS_METADATA_ROOT_CONTEXT + "/brand/partner/packaging-profile",
                regenratePNLAll: POS_METADATA_ROOT_CONTEXT + "/unit/regenerate-pnl/all",
                regenratePNLUnit: POS_METADATA_ROOT_CONTEXT + "/unit/regenerate-pnl/unit",
                regenerateFinalizedForAll: POS_METADATA_ROOT_CONTEXT + "/unit/regenerate-pnl/finalized",
                regenerateClosedForAll: POS_METADATA_ROOT_CONTEXT + "/unit/regenerate-pnl/closed",
                getStockReport: KETTLE_JOB_CONTEXT + "/stock-data",
                setCrmBannerDetail: POS_METADATA_ROOT_CONTEXT + "/set-crm-app-banner-detail",
                crmScreenUploadImage: POS_METADATA_ROOT_CONTEXT + "/upload-crm-screen-image",
                getCrmScreenType: POS_METADATA_ROOT_CONTEXT + "/get-crm-screen-type",
                resetMeterReading: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/reset-meter-readings",
                getCrmAppScreenDetail: POS_METADATA_ROOT_CONTEXT + "/get-crm-app-banner-detail",
                cloneCRMBanner: POS_METADATA_ROOT_CONTEXT + "/clone-crm-app-banner-detail",
                getCancellationalMetaData: POS_METADATA_ROOT_CONTEXT + "/metadata-by-category"
            },
            versionManagement: {
                getUnitActiveVersion : UNIT_METADATA_ROOT_CONTEXT + "/get-unit-version-detail",
                getAllUnitEvents : UNIT_METADATA_ROOT_CONTEXT + "/get-all-unit-version-event",
                getAllActiveVersions : UNIT_METADATA_ROOT_CONTEXT + "/get-active-cafe-pos-version",
                createUpdateVersionEvent : UNIT_METADATA_ROOT_CONTEXT + "/create-update-version-event",
                getCompatibilityVersions : UNIT_METADATA_ROOT_CONTEXT + "/get-compatible-Version"
            },
            feedbackQuestionManagement: {
                updateFeedbackQuestion : UNIT_METADATA_ROOT_CONTEXT + "/update-feedback-questions",
                getFeedbackQuestion: UNIT_METADATA_ROOT_CONTEXT + "/get-feedback-questions",
                getAllFeedbackUnitMapping : UNIT_METADATA_ROOT_CONTEXT + "/get-feedback-unit-mapping",
                addFeedbackUnitMapping : UNIT_METADATA_ROOT_CONTEXT + "/add-feedback-unit-mapping",
                updateFeedbackUnitMapping : UNIT_METADATA_ROOT_CONTEXT + "/update-feedback-unit-mapping",
                changeQuestionStatus : UNIT_METADATA_ROOT_CONTEXT + "/change-question-status",
                bulkUpdateFeedbackUnitMapping : UNIT_METADATA_ROOT_CONTEXT + "/bulk-update-feedback-unit-mapping"
            },
            users: {
                login: USER_SERVICES_ROOT_CONTEXT + "/login",
                // adminLogin:USER_SERVICES_ROOT_CONTEXT+"/admin/login",
                changePassCode: USER_SERVICES_ROOT_CONTEXT + "/changePasscode",
                logout: USER_SERVICES_ROOT_CONTEXT + "/logout",
                verifyUserForCancellation: USER_SERVICES_ROOT_CONTEXT + "/verifyUserForCancellation"
            },
            customer: {
                signin: CRM_SERVICES_ROOT_CONTEXT + "signin",
                profileView: CUSTOMER_SERVICES_ROOT_CONTEXT + "/profile/view",
                profileBlackList: CUSTOMER_SERVICES_ROOT_CONTEXT + "/profile/blacklist",
                profileBlackListRemove: CUSTOMER_SERVICES_ROOT_CONTEXT + "/profile/blacklist/remove",
                updateCrmScreenUrl: CRM_SERVICES_ROOT_CONTEXT + "/update-crm-screen-url",
                updateCrmScreenStatus: CRM_SERVICES_ROOT_CONTEXT + "/update-crm-screen-status",
                crmFaceIdDataUpdate: CRM_SERVICES_ROOT_CONTEXT + "/face-it/opt-out-customer/contact-number",
                uploadCustomerSheet: CUSTOMER_SERVICES_ROOT_CONTEXT + "/upload-customer",
                usubscribeWhatsapp: CUSTOMER_SERVICES_ROOT_CONTEXT + "/profile/unsub?mode=whatsapp",
                subscribeWhatsapp: CUSTOMER_SERVICES_ROOT_CONTEXT + "/profile/sub?mode=whatsapp",
                usubscribeSMS: CUSTOMER_SERVICES_ROOT_CONTEXT + "/profile/unsub?mode=sms",
                subscribeSMS: CUSTOMER_SERVICES_ROOT_CONTEXT + "/profile/sub?mode=sms"
            },
            unitMetaData: {
                unit: UNIT_METADATA_ROOT_CONTEXT + "/unit-data",
                activeUnits: UNIT_METADATA_ROOT_CONTEXT + "/all-active-units",
                unitProduct: UNIT_METADATA_ROOT_CONTEXT + "/unit-product-data",
                unitProducts: UNIT_METADATA_ROOT_CONTEXT + "/unit-products",
                allUnitContacts: DATAHUB_METADATA_CACHE_ROOT_CONTEXT + 'all-unit-contacts',
                regions: UNIT_METADATA_ROOT_CONTEXT + "/regions",
                cities: UNIT_METADATA_ROOT_CONTEXT + "/cities",
                allCities: UNIT_METADATA_ROOT_CONTEXT + "/all-cities",
                locationByZone: UNIT_METADATA_ROOT_CONTEXT + "/location-by-zone",
                companies: UNIT_METADATA_ROOT_CONTEXT + "/companies",
                divisions: UNIT_METADATA_ROOT_CONTEXT + "/divisions",
                departments: UNIT_METADATA_ROOT_CONTEXT + "/departments",
                designations: UNIT_METADATA_ROOT_CONTEXT + "/designations",
                families: UNIT_METADATA_ROOT_CONTEXT + "/families",
                cafeType: UNIT_METADATA_ROOT_CONTEXT + "/cafeType",
                taxProfiles: UNIT_METADATA_ROOT_CONTEXT + "/tax-profiles",
                units: UNIT_METADATA_ROOT_CONTEXT + "/units",
                allUnits: UNIT_METADATA_ROOT_CONTEXT + "/all-units",
                allUnitsList: UNIT_METADATA_ROOT_CONTEXT + "/all-units-list",
                addUnit: UNIT_METADATA_ROOT_CONTEXT + "/unit/add",
                updateUnit: UNIT_METADATA_ROOT_CONTEXT + "/unit/update",
                activateUnit: UNIT_METADATA_ROOT_CONTEXT + "/unit/activate",
                deactivateUnit: UNIT_METADATA_ROOT_CONTEXT + "/unit/deactivate",
                changeUnitLiveStatus: UNIT_METADATA_ROOT_CONTEXT + "/unit/change-unit-live-status",
                listTypes: UNIT_METADATA_ROOT_CONTEXT + "/listTypes",
                listTypesByTypes: UNIT_METADATA_ROOT_CONTEXT + "/listTypes-by-type",
                refreshUnitCache: UNIT_METADATA_ROOT_CONTEXT + "/unit/refresh-cache",
                activateScmUnit: SCM_UNIT_MANAGEMENT_ROOT_CONTEXT + "unit-activate",
                getAllMasterUnits: SCM_UNIT_MANAGEMENT_ROOT_CONTEXT + "get-all-master-units",
                nsoEventAssets: SCM_UNIT_MANAGEMENT_ROOT_CONTEXT + "fetch-nso-event-assets",
                deactivateScmUnit: SCM_UNIT_MANAGEMENT_ROOT_CONTEXT + "unit-deactivate",
                subCategories: UNIT_METADATA_ROOT_CONTEXT + "/sub-categories",
                viewUnitDeliveryPartners: UNIT_METADATA_MANAGMENT_ROOT_CONTEXT + "unit/delivery-partner/",
                addUpdateDeliveryPartners: UNIT_METADATA_MANAGMENT_ROOT_CONTEXT + "unit/delivery-partner/update",
                getAllManualBillBookDetail: UNIT_METADATA_MANAGMENT_ROOT_CONTEXT + "get-all-manual-bill-book-detail",
                getAllManualBillDetail: UNIT_METADATA_MANAGMENT_ROOT_CONTEXT + "get-all-manual-bill-detail",
                getListDataByGrpAndCat: UNIT_METADATA_MANAGMENT_ROOT_CONTEXT + "list-by-grp-cat",
                country: UNIT_METADATA_ROOT_CONTEXT + "/countries",
                state: UNIT_METADATA_ROOT_CONTEXT + "/states",
                location: UNIT_METADATA_ROOT_CONTEXT + "/locations",
                monkConfMetadata: UNIT_METADATA_ROOT_CONTEXT + "/monk-configuration/metadata",
                saveMonkConfData: UNIT_METADATA_ROOT_CONTEXT + "/monk-configuration/add",
                unitConfData: UNIT_METADATA_ROOT_CONTEXT + "/monk-configuration/conf-data",
                unitPartnerProductsTrimmed: UNIT_METADATA_ROOT_CONTEXT + "/unit/partner/products/trimmed",
                productProfile: UNIT_METADATA_ROOT_CONTEXT + "/region/products",
                brandProductProfile: UNIT_METADATA_ROOT_CONTEXT + "/brand/partner/products",
                metadata: UNIT_METADATA_ROOT_CONTEXT + "/metadata",
                restrictedApplication: APPLICATION_INSTALLATION_ROOT_CONTEXT + "application-list",
                updateRestrictedApplication: APPLICATION_INSTALLATION_ROOT_CONTEXT + "update-application-restriction",
                brandPartnerMapping: UNIT_METADATA_ROOT_CONTEXT + "/brand-partner-mapping",
                EditSCMUnit: SCM_UNIT_MANAGEMENT_ROOT_CONTEXT + "scm-unit-update",
                checkForPendingSCMTransactions: SCM_METADATA_ROOT_CONTEXT + "validate-for-deactivation",
                setSuggestWalletStatus : UNIT_METADATA_ROOT_CONTEXT + "/activate/suggest/wallet",
                addPartnerEdcMapping : UNIT_METADATA_ROOT_CONTEXT + "/add-partner-edc--mapping",
                getPartnerEdcMapperList : UNIT_METADATA_ROOT_CONTEXT + "/get-partner-edc-mapping-list",
                updatePartnerEdcMapperList : UNIT_METADATA_ROOT_CONTEXT + "/update-partner-edc-mapping-list",
                getAllActiveVersions : UNIT_METADATA_ROOT_CONTEXT + "/get-active-cafe-pos-version",
                updateApplicationVersion : UNIT_METADATA_ROOT_CONTEXT + "/update-cafe-pos-version",
                uploadApkBuild : UNIT_METADATA_ROOT_CONTEXT + "/upload-apk-build",
                downloadApkBuild : UNIT_METADATA_ROOT_CONTEXT + "/get-apk-build",
                addVersionCompatibility : UNIT_METADATA_ROOT_CONTEXT + "/add-version-compatibility",
                getCompatiblePosVersion : UNIT_METADATA_ROOT_CONTEXT + "/get-compatible-pos-version",
                addUnitContacts: UNIT_METADATA_ROOT_CONTEXT + '/add-unit-contacts',
                getPartnerDqrMapperList : UNIT_METADATA_ROOT_CONTEXT + "/get-partner-dqr-mapping-list",
                addPartnerDqrMapping : UNIT_METADATA_ROOT_CONTEXT + "/add-partner-dqr-mapping",
                updatePartnerDqrMapperList : UNIT_METADATA_ROOT_CONTEXT + "/update-partner-dqr-mapping-list",
                syncUnitProductPrice : UNIT_METADATA_ROOT_CONTEXT + "/sync-unit-product-price",
                getUnitBrandMappings : UNIT_METADATA_ROOT_CONTEXT + "/get-unit-brand-mappings",
                addUnitBrandMappings : UNIT_METADATA_ROOT_CONTEXT + "/add-unit-brand-mappings",
                getMonkMetaData : MONK_META_DATA + "/get-monk-metadata-by-unitId",
                updateMonkMetaData : MONK_META_DATA + "/update-monk-metadata-by-unitId",
                unitCities: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "unit-cities",
                unitCityMapping: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "unit-city-mapping"
            },
            productMetaData: {
                deactivateProductMapping: PRODUCT_METADATA_ROOT_CONTEXT + "/product/mapping/deactivate",
                activateProductMapping: PRODUCT_METADATA_ROOT_CONTEXT + "/product/mapping/activate",
                unitProductPriceUpdate: PRODUCT_METADATA_ROOT_CONTEXT + "/unit-product/price/update",
                productPriceUpdate: PRODUCT_METADATA_ROOT_CONTEXT + "/product/price/update",
                changeUnitProductProfile: PRODUCT_METADATA_ROOT_CONTEXT + "/product/profile/change",
                unitProductProfileUpdate: PRODUCT_METADATA_ROOT_CONTEXT + "/unit-product-profile/update",
                products: PRODUCT_METADATA_ROOT_CONTEXT + "/products",
                productsByBrand: PRODUCT_METADATA_ROOT_CONTEXT + "/products-by-brand",
                productsActive: PRODUCT_METADATA_ROOT_CONTEXT + "/products/active",
                activeWebProducts: PRODUCT_METADATA_ROOT_CONTEXT + "/products/active/web",
                productsInfo: PRODUCT_METADATA_ROOT_CONTEXT + "/product-info",
                productPriceMapping: PRODUCT_METADATA_ROOT_CONTEXT + "/product/price/mappings",
                vendor: PRODUCT_METADATA_ROOT_CONTEXT + "/vendor",
                deactivateProduct: PRODUCT_METADATA_ROOT_CONTEXT + "/product/deactivate",
                activateProduct: PRODUCT_METADATA_ROOT_CONTEXT + "/product/activate",
                addProduct: PRODUCT_METADATA_ROOT_CONTEXT + "/product/add",
                updateProduct: PRODUCT_METADATA_ROOT_CONTEXT + "/product/update",
                classifications: PRODUCT_METADATA_ROOT_CONTEXT + "/classifications",
                webType: PRODUCT_METADATA_ROOT_CONTEXT + "/categories/web",
                imageCategoryType: PRODUCT_METADATA_ROOT_CONTEXT + "/image-category-type",
                uploadedProductImage: PRODUCT_METADATA_ROOT_CONTEXT + "/uploaded-product-image",
                uploadedProductDimensionImage: PRODUCT_METADATA_ROOT_CONTEXT + "/uploaded-product-dimension-image",
                allProductImages: PRODUCT_METADATA_ROOT_CONTEXT + "/all-uploaded-product-image",
                uploadedProductImagesById: PRODUCT_METADATA_ROOT_CONTEXT + "/uploaded-product-images-for-productIds",
                uploadedProductImagesByIdTemp: PRODUCT_METADATA_ROOT_CONTEXT + "/uploaded-product-dimension-images-for-productIds",
                uploadProductImage: PRODUCT_METADATA_ROOT_CONTEXT + "/upload-product-image",
                uploadProductDimensionImage: PRODUCT_METADATA_ROOT_CONTEXT + "/upload-product-dimension-image",
                getProductDescription: PRODUCT_METADATA_ROOT_CONTEXT + "/get-product-description",
                updateProductDescription: PRODUCT_METADATA_ROOT_CONTEXT + "/add/update-product-description",
                getNutritionDetail: PRODUCT_METADATA_ROOT_CONTEXT + "/product/nutrition",
                getNutritionDetailBySource: PRODUCT_METADATA_ROOT_CONTEXT + "/product/nutrition/data",
                updateNutritionDetailBySource: PRODUCT_METADATA_ROOT_CONTEXT + "/update/product/nutrition",
                productPriceMappingsProfileWise: PRODUCT_METADATA_ROOT_CONTEXT + "/product/price/mappings/selectedRegionAndProfileWise",
                unitProductsMappings: PRODUCT_METADATA_ROOT_CONTEXT + "/get/unit-product-mappings",
                unitProductsMappingsToEmail: PRODUCT_METADATA_ROOT_CONTEXT + "/get/unit-product-mappings/email",
                recipeProfileInactivateCheck: PRODUCT_METADATA_ROOT_CONTEXT + "/recipe-profile-inactive-check",
                postProductCheckList: PRODUCT_METADATA_ROOT_CONTEXT + "/product-checklist",
                getProductCheckList: PRODUCT_METADATA_ROOT_CONTEXT + "/product-checklist/get",
                bulkUpdatedUnitProductProfiles: PRODUCT_METADATA_ROOT_CONTEXT + "/bulk/product-profile-change",
                getUnitsShortByBrandId: PRODUCT_METADATA_ROOT_CONTEXT + "/get/units-short-by-brand",
            },
            genericExcelManagement: {
                getListDataFromExcel: GENERIC_EXCEL_ROOT_CONTEXT + "/excel/parse",
                getExcelFromData: GENERIC_EXCEL_ROOT_CONTEXT + "/excel/generate"
            },
            priceProfileManagement : {
                addPriceProfile : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/add-price-profile",
                findPriceProfiles : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/find-price-profile",
                togglePriceProfileStatus : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/toggle-price-profile-status",
                togglePriceProfileVersionStatus : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/toggle-price-profile-version-status",
                getProfileProductMappings : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/get-price-profile-product-mappings",
                saveProfileProductMappings : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/save-price-profile-product-mappings",
                getUnitPriceProfileMappings : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/get-unit-price-profile-mappings",
                addUnitPriceProfileMappings : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/save-unit-price-profile-mappings",
                getUnitPriceProfileMappingsSheet  : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/get-unit-price-profile-sheet",
                getProductPriceProfileMappingsSheet  : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/get-price-profile-product-sheet",
                uploadBulkProductPriceProfileSheet : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/product/price-profile/bulk-update",
                createPriceProfileVersion : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/create-price-profile-version",
                createPriceProfileVersions : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/create-price-profile-versions",
                getMaxVersion : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/get-max-version",
                uploadBulkUnitPriceSheet : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/unit/price-profile/bulk-update",
                toggleBulkPriceProfileVersion : PRICE_PROFILE_METADATA_ROOT_CONTEXT + "/toggle-bulk-price-profile-version-status"

            },
            brandMetaData: {
                getAllBrands: BRAND_METADATA_ROOT_CONTEXT + "/brands",
                getAllBrandsShort: BRAND_METADATA_ROOT_CONTEXT + "/brands-short",
                getAllBrandAttributes: BRAND_METADATA_ROOT_CONTEXT + "/all-brand-attributes",
                updateBrandAttributes: BRAND_METADATA_ROOT_CONTEXT + "/update-brand-attributes",
                getAllUnitPartnerBrandMappings: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-mappings",
                getAllUnitPartnerBrandMapping: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-mapping",
                getUnitPartnerBrandMetadataKeys: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-metadata/keys",
                getUnitPartnerBrandMetadata: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-metadata",
                addUnitPartnerBrandMetadata: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-metadata/add",
                addUnitPartnerBrandMetadataBulk: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-metadata/add/multiple",
                activateUnitPartnerBrandMetadata: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-metadata/activate",
                deactivateUnitPartnerBrandMetadata: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-metadata/deactivate",
                uploadMonkMetadata: BRAND_METADATA_ROOT_CONTEXT + "/monk-meta-data",
                getCompanyBrandMapping: MASTER_METADATA_ROOT_CONTEXT + "get-company-brand-mapping"
            },
            aliasManagement: {
                getEntityAliasBYIdandType: ALIAS_MANAGEMENT_ROOT_CONTEXT + "/entity-alias-by-id-type",
                addAliasMapping: ALIAS_MANAGEMENT_ROOT_CONTEXT + "/add/entity-alias",
                updateAliasMapping: ALIAS_MANAGEMENT_ROOT_CONTEXT + "/update/entity-alias"
            }, reportMetaData: {
                wowReport: REPORT_METADATA_ROOT_CONTEXT + "/report/expense/execute/wow",
                momReport: REPORT_METADATA_ROOT_CONTEXT + "/report/expense/execute/mom",
                revenueCertificate: REPORT_METADATA_ROOT_CONTEXT + "/report/revenueCertificate",
                revenueCertificateMails: REPORT_METADATA_ROOT_CONTEXT + "/report/revenueCertificateMails",
                submitUnitCertificateMapping : REVENUE_CERTIFICATE + "submit-unitCertificateMapping",
                getUnitCertificateMapping : REVENUE_CERTIFICATE + "get-unitCertificateMapping",
            }, reportServiceMetaData: {
                reportEnviroments: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/environments",
                adHocReports: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/adhoc-reports-list",
                adHocReportsV1: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/adhoc-reports-list/new",
                reportCategories: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/categories",
                reportCategoriesV1: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/categories/new",
                reportExecute: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/execute",
                reportVersionHistory: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/report-version-details",
                reportCategoryNew: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/report-categories",
                reportType: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/report-type",
                addNewReport: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/add-new",
                updateReportStatus: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/change-status",
                markDefaultVersion: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/mark-default-version",
                uploadNewReportVersion: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/upload-new-report-version",
                downloadNewReportVersion: REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/report/report-download",
                getRedeemedCouponSheet : REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/get-redeemed-coupon-sheet",
                uploadRedeemedCouponSheet : REPORT_SERVICE_METADATA_ROOT_CONTEXT + "/upload-redeemed-coupon-sheet"

            },
            kioskManagement: {
                kioskCompany: KIOSK_ROOT_CONTEXT + "company-details",
                kioskOffice: KIOSK_ROOT_CONTEXT + "office-details",
                kioskLocation: KIOSK_ROOT_CONTEXT + "location-details",
                kioskAssignUnit: KIOSK_ROOT_CONTEXT + "assign-unit",
                kiosCompanyStatus: KIOSK_ROOT_CONTEXT + "company-details/change-status",
                kiosOfficeStatus: KIOSK_ROOT_CONTEXT + "office-details/change-status",
                kiosLocationStatus: KIOSK_ROOT_CONTEXT + "location-details/change-status"
                // / viewKioskCompany:KIOSK_ROOT_CONTEXT+"company-details",
            },
            neoData: {
                getTags: NEO_CONTEXT + "ncm/tags",
                addTags: NEO_CONTEXT + "ncm/tag/add",
                updateTags: NEO_CONTEXT + "ncm/tag/update",
                typeTags: NEO_CONTEXT + "ncm/tag/types",
                getLocalities: NEO_CONTEXT + "ncm/localities/get-localities",
                addLocalities: NEO_CONTEXT + "ncm/localities/add-localities",
                removeLocalities: NEO_CONTEXT + "ncm/localities/remove-localities",
                updateLocalities: NEO_CONTEXT + "ncm/localities/update-localities",
                uploadBulkLocalities: NEO_CONTEXT + "ncm/localities/add-bulk-localities",
            },
            neoCache: {
                getProductRecipes: NEO_CONTEXT + "nc/p/r",
                refreshCache: NEO_CONTEXT + "nc/r",
                refreshUnitCache: NEO_CONTEXT + "nc/ru",
            },
            customerProfile: {
                refer: CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT + "/refer"
            },
            referral: {
                submit: REFERRAL_SERVICES_ROOT_CONTEXT + "/submit/code",
                validate: REFERRAL_SERVICES_ROOT_CONTEXT + "/validate",
                generateToken: REFERRAL_SERVICES_ROOT_CONTEXT + "/generate-token"
            },
            monkRecipeManagement: {
                addRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/add",
                updateRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/update",
                findRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/find-recipe",
                findRecipeByName: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/find-recipe/name",
                removeRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/remove",
                findAll: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/findAll",
                downloadAllRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/get-all-recipe-dump",
                findContainingName: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/findContainigName",
                findRecipeVersionByStatus: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/find-version-by-status",
                activateCurrentCreatedVersion: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/activate-current-version",
                createNewVersion: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/create-new-version",
                saveNewVersions: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/version/save",
                getMonkRecipeProfileBasicDetail: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/profile/basic-detail",
                getMonkRecipeProfileSheet: RECIPE_SERVICES_ROOT_CONTEXT + "/monk/profile/sheet",

            },
            recipeManagement: {
                ingredientType: RECIPE_SERVICES_ROOT_CONTEXT + "/ingredient-type",
                addRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/add",
                addRecipeMedia: RECIPE_SERVICES_ROOT_CONTEXT + "/add-media",
                getRecipeStepByRecipeId: RECIPE_SERVICES_ROOT_CONTEXT + "/get-recipe-steps-media",
                uploadRecipeMedia: RECIPE_SERVICES_ROOT_CONTEXT + "/upload-recipe-media",
                condimentSheetTemplate: RECIPE_SERVICES_ROOT_CONTEXT + "/recipe-condiment-sheet-template",
                bulkUploadRecipeCondiment: RECIPE_SERVICES_ROOT_CONTEXT + "/bulk-upload-recipe-condiment",
                addCloneRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/add-clone",
                updateRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/update",
                findRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/find-recipe",
                findRecipeByUom: RECIPE_SERVICES_ROOT_CONTEXT + "/find-recipe-by-uom",
                findRecipeByName: RECIPE_SERVICES_ROOT_CONTEXT + "/find-recipe/name",
                removeRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/remove",
                getLogs: RECIPE_SERVICES_ROOT_CONTEXT + "/get-logs",
                findAll: RECIPE_SERVICES_ROOT_CONTEXT + "/findAll",
                downloadRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/get-recipe-dump",
                downloadAllRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/get-all-recipe-dump",
                downloadAllRecipeDetailWithCost: RECIPE_SERVICES_ROOT_CONTEXT + "/get-all-recipe-with-cost-dump",
                downloadAllRecipeCost: RECIPE_SERVICES_ROOT_CONTEXT + "/get-all-recipe-cost-dump",
                recipeCostByKey: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "recipe/cost/key",
                recipeCostByDetail: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "recipe/cost/detail",
                recipeCostByDetailNew: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "recipe/cost/detail/new",
                findContainingName: RECIPE_SERVICES_ROOT_CONTEXT + "/findContainigName",
                recipeProfiles: RECIPE_SERVICES_ROOT_CONTEXT + "/recipe/profiles",
                recipeProfileOfProduct: RECIPE_SERVICES_ROOT_CONTEXT + "/recipe-profile-product",
                recipeProfileOfProductDimension: RECIPE_SERVICES_ROOT_CONTEXT + "/recipe-profile-product-dimension",
                recipeProfileOfProductsDimensions: RECIPE_SERVICES_ROOT_CONTEXT + "/recipe-profile-products-dimensions",
                getDispenseTag: RECIPE_SERVICES_ROOT_CONTEXT + "/get-dispense-tag",
                saveDispenserData: RECIPE_SERVICES_ROOT_CONTEXT + "/save-dispenser-data",
                getDispenserData: RECIPE_SERVICES_ROOT_CONTEXT + "/get-dispenser-data",
                updateRecipeProfileStatus: RECIPE_SERVICES_ROOT_CONTEXT + "/update-recipe-profile-status",
                srcToCondimentMap : RECIPE_SERVICES_ROOT_CONTEXT + "/recipe-src-condiment",
                approveRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/approve-recipe",
                rejectRecipe: RECIPE_SERVICES_ROOT_CONTEXT + "/reject-recipe",
                findRecipeById : RECIPE_SERVICES_ROOT_CONTEXT + "/find-recipe-by-recipe-id",
                reActivateRecipe : RECIPE_SERVICES_ROOT_CONTEXT + "/re-activate-recipe"
            },
            scmRecipeManagement: {
                findAll: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/findAll",
                findAllBasic: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/findAllBasic",
                downloadAllRecipe: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/get-all-recipe-dump",
                downloadAllRecipeInstruction: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/get-all-recipe-instruction-dump",
                downloadAllRecipeDetailWithCost: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/get-all-recipe-with-cost-dump",
                downloadAllRecipeCost: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/get-all-recipe-cost-dump",
                findContainingName: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/findContainigName",
                addIteration: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/addIteration",
                updateIteration: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/updateIteration",
                getIterationForProduct: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/iterationForProduct",
                iterationForConstruct: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/iterationForConstruct",
                validateIterationName: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/validateIterationName",
                updateIterationStatus: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/updateIterationStatus",
                updateIterationsComment: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/updateIterationsComment",
                archivedIterationsForConstruct: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/archivedIterationConstruct",
                archivedIterationsForProduct: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/archivedIterationProduct",
                allSCMConstruct: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/allConstruct",
                validateConstructName: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/validateConstructName",
                getAllIngredientInstructions: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/getAllIngredientInstructions",
                uploadSCMMedia: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/uploadSCMrecipemedia",
                updateIterationImages: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/updateIterationImages",
                checkRecipeForSemiFinishedProducts: SCM_RECIPE_SERVICES_ROOT_CONTEXT + "/check-recipe-for-sm-products"
            },
            offerManagement: {
                marketingPartner: OFFER_MANAGEMENT_ROOT_CONTEXT + "/marketing-partner",
                offerCategory: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offer-category",
                paymentModes: OFFER_MANAGEMENT_ROOT_CONTEXT + "/payment-modes",
                marketingPartnerDeactivate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/marketing-partner/deactivate",
                marketingPartnerActivate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/marketing-partner/activate",
                marketingPartnerAdd: OFFER_MANAGEMENT_ROOT_CONTEXT + "/marketing-partner/add",
                offerCoupons: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offer/coupons",
                offers: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offers",
                offersAdd: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offers/add",
                offersUpdate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offers/update",
                couponAvailablity: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/availablity",
                couponAdd: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/add",
                couponUpdate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/update",
                couponBulkUpdate : OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/bulk/update",
                couponMappingUpdate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/update/mappings",
                couponUpdateall: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/update/all",
                couponAuto: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/auto",
                couponMappingActivate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/mapping/activate",
                couponMappingDeactivate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/mapping/deactivate",
                couponSearch: OFFER_MANAGEMENT_ROOT_CONTEXT + "/coupon/search",
                offerAccountsCategories: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offerAccountsCategories",
                offer: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offer",
                appOfferTypes: OFFER_MANAGEMENT_ROOT_CONTEXT + "/app-offer-types",
                appOfferActionTypes: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offer-action-type",
                getAllAppOffers: OFFER_MANAGEMENT_ROOT_CONTEXT + "/getAllAppOffers",
                getAppOffersByPartnerId: OFFER_MANAGEMENT_ROOT_CONTEXT + "/getAppOffersByPartnerId",
                updateStatusForAppOffers: OFFER_MANAGEMENT_ROOT_CONTEXT + "/updateStatusForAppOffers",
                updateOrderingForAppOffers: OFFER_MANAGEMENT_ROOT_CONTEXT + "/updateOrderingForAppOffers",
                uploadImage: OFFER_MANAGEMENT_ROOT_CONTEXT + "/upload-offer-Image",
                addAppOffer: OFFER_MANAGEMENT_ROOT_CONTEXT + "/add-new-app-offer",
                updateAppOffer: OFFER_MANAGEMENT_ROOT_CONTEXT + "/update-app-offer",
                getBannerMetadata: OFFER_MANAGEMENT_ROOT_CONTEXT + "/get-banner-metadata",
                getCategoryFromMenuSequence: OFFER_MANAGEMENT_ROOT_CONTEXT + "/get-active-app-menu-category",
                setSignupCouponMapping: MASTER_OFFER_ROOT_CONTEXT + "signup/coupon-detail",
                addCampaignDetail: OFFER_MANAGEMENT_ROOT_CONTEXT + "/add-new-campaign",
                getAllCampaignDetails: OFFER_MANAGEMENT_ROOT_CONTEXT + "/get-all-campaigns",
                getCampaignsByCampaignDesc: OFFER_MANAGEMENT_ROOT_CONTEXT + "/get-campaigns-by-campaign-desc",
                getCampaignById: OFFER_MANAGEMENT_ROOT_CONTEXT + "/get-campaign-by-id",
                updateCampaignStatus: OFFER_MANAGEMENT_ROOT_CONTEXT + "/update-campaign-status",
                updateCampaignDetail: OFFER_MANAGEMENT_ROOT_CONTEXT + "/update-campaign",
                updateCampaignShortUrl: OFFER_MANAGEMENT_ROOT_CONTEXT + "/update-campaign-short-url",
                uploadCampaignImage: OFFER_MANAGEMENT_ROOT_CONTEXT + "/upload-campaign-image",
                createCampaignShortUrl: OFFER_MANAGEMENT_ROOT_CONTEXT + "/create-campaign-short-url",
                uploadDeliveryCouponSheet: OFFER_MANAGEMENT_ROOT_CONTEXT + "/upload-delivery-coupon-sheet",
                downloadDeliveryCouponSheetTemplate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/delivery-coupon-sheet-template",
                validateDeliveryCoupon: OFFER_MANAGEMENT_ROOT_CONTEXT + "/validate-delivery-coupon",
                campaignListByValidDate: OFFER_MANAGEMENT_ROOT_CONTEXT + "/campaign-list-by-valid-date",
                refreshCashbackOfferCache: CASHBACK_OFFER_ROOT_CONTEXT + "/clear-cache",
                addCashbackOfferCache: CASHBACK_OFFER_ROOT_CONTEXT + "/add-offer",
                refreshCashbackOfferCacheV2: TRANSACTION_SERVICE_CONTEXT + "cashbk-offer-resource/clear-cache",
                getUnitWiseOffers: CASHBACK_OFFER_ROOT_CONTEXT + "/get-offers",
                getOfferDescriptionMetadataByOfferId: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offer-description-metadata",
                getAllOfferDescriptionMetadataByOfferId: OFFER_MANAGEMENT_ROOT_CONTEXT + "/offer-description-metadata",
                updateOfferDescriptionMetadata: OFFER_MANAGEMENT_ROOT_CONTEXT + "/update-offer-description-metadata"
            },
            kettleOfferManagement: {
                generateToken: KETTLE_OFFER_MANAGEMENT_ROOT_CONTEXT + "/generate-token"
            },
            customerOfferManagement: {
                channelPartners: CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/channel-partners",
                isEmpApplicable: CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/is-emp-applicable",
                deactivateEMPBenefits: CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/deactivate-emp-benefits"
            },
            campaignCache: {
                clearCampaignCache: POS_METADATA_ROOT_CONTEXT + "/clear-campaign-cache"
            },
            masterCacheManagement: {
                refreshMasterCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-master-cache",
                refreshMasterCacheWithInventory: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-master-cache-with-inventory",
                refreshLocalityCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-locality-cache",
                refreshTaxCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-tax-cache",
                refreshRecipeCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-recipe-cache",
                refreshPaymentCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-payment-cache",
                refreshDivisionCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-division-cache",
                refreshDepartmentCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-department-cache",
                refreshDesignationCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-designation-cache",
                refreshTaxProfileCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-tax-profile-cache",
                refreshDenominationCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-denomination-cache",
                refreshEmployeeCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-employee-cache",
                refreshHodDetailCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-hod-cache",
                refreshPreAuthenticatedApiCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-pre-authenticated-apis-cache",
                refreshUnitCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-unit-cache",
                refreshUnitCacheWithInventory: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-unit-cache-with-inventory",
                refreshListDataCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-list-data-cache",
                refreshAddonDataCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-addon-data-cache",
                refreshProductCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-product-cache",
                refreshCancellationCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-cancellation-cache",
                refreshKioskCompanies: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-kiosk-companies",
                refreshExternalApi: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-external-api",
                refreshEnvironmentProps: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-environment-props",
                refreshLocations: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-locations",
                refreshItemPerTicket: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-item-per-ticket",
                refreshBrandMetadata: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-brand-meta-data",
                refreshEntityAliasData: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-entity-alias-data",
                refreshPartnerBrandMappingData: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "unit-partner-brand-mapping-data",
                reloadUnitMenuSequenceMapping: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "unit-partner-menu-mapping-data",
                reloadUnitChannelPartnerMapping: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "reload-unit-channel-partner",
                reloadChannelPartner: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "reload-channel-partner",
                reloadUnitPartnerBrandMetadata: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "reload-unit-partner-brand-mapping-metadata",

                downloadLocalities: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "download/localities",
                uploadLocalities: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "upload/localities",
                uploadLocalitiesObj: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "upload/localities-obj",
                updateLocality: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "update/locality",
                localities: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "localities",
                refreshRegionCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-region-cache",
                refreshReferenceMetadataCache : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-cache-reference" ,
                refreshUnitPartnerEdcMapping : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-unit-partner-edc-mapping",
                refreshSrcCondimentMapping : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-src-condiment-mapping",
                refreshGroupCondimentMapping : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-group-condiment-mapping",
                refreshSubscriptionCache: UNIT_METADATA_ROOT_CONTEXT + "/update-subscription-info",
                refreshAllExperiments : AB_TESTING_ROOT_CONTEXT + "clear-all-active-experiments",
                refreshExperimentToScenarioMappings : AB_TESTING_ROOT_CONTEXT + "clear-all-active-experiment-to-scenario-mappings",
                refreshScenarioToExperimentMappings : AB_TESTING_ROOT_CONTEXT + "clear-all-active-scenario-to-experiment-mappings",
                clearCustomerAppliedCouponCache : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "clear-customer-applied-coupon-cache",
                refreshUnitMonkRecipeProfilesVersion : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-unit-monk-recipe-profile-version",
                refreshWorkStationsStationCategories : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-work-stations-station-categories",
                refreshPriceCategoryWiseProductsPrice : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-price-category-wise-product-price",
                refreshUnitDroolVersionMapping : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-unit-drool-version-mapping",
                refreshDispenserItemCanisterCache : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-dispenser-canister-item-cache",
                refreshPriceProfileProductsMappingCache : MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-price-profile-products-cache",
                refreshCompanyBrandsMappingCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-company-brands-mapping-cache",
                refreshUnitBrandMappingsCache: MASTER_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "refresh-unit-brand-mappings-cache"
            },
            sessionCacheManagement: {
                clearAllPosSession: SESSION_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "clear-all-pos-session",
                clearUnitPosSession: SESSION_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "clear-unit-pos-session",
                clearUnitSession: SESSION_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "clear-unit-session",
                clearUserPosSession: SESSION_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "clear-user-pos-session",
                clearUserSession: SESSION_CACHE_METADATA_MANAGEMENT_ROOT_CONTEXT + "clear-user-session",
            },
            scmCacheManagement: {
                clearProductionLineCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "clear-production-line-cache",
                refreshCompleteCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-complete-cache",
                refreshAttributeDefinitionCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-attribute-definition-cache",
                refreshCategoryDefinitionCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-category-definition-cache",
                refreshPackagingDefinitionCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-packaging-definition-cache",
                refreshCategoryAttributeMappingCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-category-attribute-mapping-cache",
                refreshProductDefinitionCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-product-definition-cache",
                refreshSkuDefinitionCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-sku-definition-cache",
                refreshUnitDetailCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-unit-detail-cache",
                refreshUnitCategoryCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-unit-category-cache",
                refreshAttributeValueCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-attribute-value-cache",
                refreshCategoryAttributeValueCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-category-attribute-value-cache",
                refreshProductPackagingMappingCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-product-packaging-mapping-cache",
                refreshCategoryPackagingMappingCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-category-packaging-mapping-cache",
                refreshSkuAttributeValueCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-sku-attribute-value-cache",
                refreshVendorDetailCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-vendor-detail-cache",
                refreshSubCategoryDefinitionCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-sub-category-definition-cache",
                refreshRegionMappingCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-region-mapping-cache",
                pendingMilkBread: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-pending-milk-bread-cache",
                clearSkuDefinitionListCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "clear-sku-definition-list-cache",
                markMilkBreadComplete: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "mark-milk-bread-complete",
                refreshStockTakeAppVersion: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-stock-take-app-version",
                refreshAssetCache : SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-asset-cache",
                refreshFulfillmentUnitMappingCache : SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-fulfillment-unit-mapping-cache",
                refreshSkusByProductIdCache : SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "refresh-skus-by-productId",
            },
            neoCacheManagement: {
                refreshCompleteCache: NEO_CONTEXT + "nc/r",
                refreshUnitCache: NEO_CONTEXT + "nc/ru",
                refreshTagsCache: NEO_CONTEXT + "ncm/tags/refresh",
                refreshCompleteRedisCache: NEO_CONTEXT + "ncm/reload",
                refreshLocationCache: NEO_CONTEXT + "ncm/location/reload",
                clearProductionLineCache: SCM_CACHE_MANAGEMENT_ROOT_CONTEXT + "clear-production-line-cache",
            },
            kettle2CacheManagement: {
                refreshAllUnit:DATAHUB_REFRESH_CACHE_ROOT_CONTEXT+"refresh-all-unit",
                refreshAllPaymentMode:DATAHUB_REFRESH_CACHE_ROOT_CONTEXT+"refresh-all-payment-mode",
                refreshAllComplimentaryCode:DATAHUB_REFRESH_CACHE_ROOT_CONTEXT+"refresh-all-complimentary-code",
                refreshAllCancellationReason:DATAHUB_REFRESH_CACHE_ROOT_CONTEXT+"refresh-all-cancellation-reasons",
                refreshProductCategory:DATAHUB_REFRESH_CACHE_ROOT_CONTEXT+"refresh-product-category",
                refreshAllProductImage:DATAHUB_REFRESH_CACHE_ROOT_CONTEXT+"refresh-product-image-data",
                refreshAllUnitMenuSequence: DATAHUB_REFRESH_CACHE_ROOT_CONTEXT + "refresh-all-menu-sequence",
                refreshUnitMenuSequence : DATAHUB_REFRESH_CACHE_ROOT_CONTEXT + "refresh-unit-menu-sequence",
                refreshDataHubMasterCache : DATAHUB_REFRESH_CACHE_ROOT_CONTEXT +"refresh-all-datahub-cache",
                refreshMenuSequence: DATAHUB_REFRESH_CACHE_ROOT_CONTEXT+"refresh-menu-sequence",
                refreshAllUnitProductDynamicPrice : DATAHUB_METADATA_CACHE_ROOT_CONTEXT+"refresh-all-unit-products-dynamic-price",
                refreshUnitProductDynamicPrice : DATAHUB_METADATA_CACHE_ROOT_CONTEXT+"refresh-unit-products-dynamic-price",
                clearABTestingCache: AB_TESTING_ROOT_CONTEXT+"clear-complete-ab-testing-cache",
                clearRecommendationCache:RECOM_SERVICE_ROOT_CONTEXT+"clear-all-recommendations",
                clearDispenserCache:TRANSACTION_SERVICE_CONTEXT + "dispenser/refresh-dispenser-cache"
            },
            userManagement: {
                managers: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/managers",
                activeEmployees: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/employees/active",
                users: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/users",
                user: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user",
                userAdd: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/add",
                updateEmpMapping: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/updateEmpMapping",
                empMapping: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/empMapping",
                userUpdateRoles: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/update/roles",
                userUpdateRolesV2: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/update/roles-v2",
                allUserRoles: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/active/roles",
                allActiveRoles: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/active/roles",
                userActivate: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/activate",
                userDeactivate: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/deactivate",
                userUnits: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/units",
                userUpdateMapping: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/update-mapping",
                userUpdate: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/update",
                userResetPasscode: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/reset-passcode",
                downloadOnboardingSheet: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/employee-onboarding-sheet",
                uploadOnboardingSheet: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/upload/employeeOnboardingSheet",
                addEmpAppMapping: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/user/addEmpAppMapping",
                getUserPolicies: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/get-user-policies",
                createUpdatePolicyRoles: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/create-update-user-roles-for-policy",
                employeesWithDepartmentDesignation: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/get-employees-with-department-designation",
                resetUpdateEmployeeUserPolicy: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/reset-update-employee-user-policy",
                uploadDocument: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/upload-document",
                getHodsList: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/get/all-hods",
                updateEmpInHodList: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/update-hod",
                employeeAttendanceMapping: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/employee-attendance/mappings",
                exportMappingsExcel: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/employee-attendance/export-mappings-excel",
                downloadTemplate: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/employee-attendance/download-template",
                bulkUpload: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "/employee-attendance/bulk-upload"
            },
            scmProductManagement: {
                metadata: SCM_METADATA_ROOT_CONTEXT + "metadata",
                getState: SCM_METADATA_ROOT_CONTEXT + "states",
                getRegionFulfillmentMappings: SCM_METADATA_ROOT_CONTEXT + "region-mappings",
                productDetail: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "product",
                productDetails: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "products",
                productBasicDetails: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "products-basic-detail",
                productBasicDetailsForRecipe: SCM_PRODUCT_MANAGEMENT_ROOT_CONTEXT + "products-basic-detail-for-recipe"
            },
            scmPriceManagement: {
                productPriceDetail: SCM_PRICE_MANAGEMENT_ROOT_CONTEXT + "price-details-for-product-list",
                scmRecipeProductCost: SCM_PRICE_MANAGEMENT_ROOT_CONTEXT + "scm-recipe-cost",
            },
            scmUnitManagement: {
                businessDate: SCM_UNIT_MANAGEMENT_ROOT_CONTEXT + "business-date"
            },
            cashManagement: {
                pullSettlementsGet: CASH_MANAGEMENT_ROOT_CONTEXT + "/pullSettlements/get",
                pullSettlementsGetByType: CASH_MANAGEMENT_ROOT_CONTEXT + "/pullSettlements/getByType",
                pullSettlementsOpenGet: CASH_MANAGEMENT_ROOT_CONTEXT + "/pullSettlements/open/get",
                pullSettlementsOpenGetByType: CASH_MANAGEMENT_ROOT_CONTEXT + "/pullSettlements/open/getByType",
                pullSettlementClose: CASH_MANAGEMENT_ROOT_CONTEXT + "/pullSettlement/close",
                downloadSlip: CASH_MANAGEMENT_ROOT_CONTEXT + "/download/slip",
                pullSettlementsGetTotalCount: CASH_MANAGEMENT_ROOT_CONTEXT + "/pullSettlements/getTotalCount",
                pullSettlementsGetByTypeTotalCount: CASH_MANAGEMENT_ROOT_CONTEXT + "/pullSettlements/getByTypeTotalCount"
            },
            checkList: {
                addCheckList: CHEKLIST_MANAGEMENT_CONTEXT + "checklist/add",
                updateCheckList: CHEKLIST_MANAGEMENT_CONTEXT + "checklist/update",
                activate: CHEKLIST_MANAGEMENT_CONTEXT + "checklist/activate",
                listTypes: CHEKLIST_METADATA_CONTEXT + "listTypes",
                categories: CHEKLIST_METADATA_CONTEXT + "categories",
                frequency: CHEKLIST_METADATA_CONTEXT + "frequency",
                station: CHEKLIST_METADATA_CONTEXT + "station",
                allCheckLists: CHEKLIST_MANAGEMENT_CONTEXT + "checklists"
            },
            checklistitem: {
                activate: CHEKLIST_MANAGEMENT_CONTEXT + "checklistitem/activate",
                deactivate: CHEKLIST_MANAGEMENT_CONTEXT + "checklistitem/deactivate"
            },
            accessControlManagement: {
                getAccessControls: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "access-controls",
                activateAccessControls: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "activate-access-control",
                deactivateAccessControls: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "deactivate-access-control",
                applications: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "applications",
                preAuthenticatedApi: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "pre-authenticated-api",
                preAuthenticatedApis: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "pre-authenticated-apis",
                deactivatePreAuthenticatedApi: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT
                    + "deactivate-pre-authenticated-api",
                activatePreAuthenticatedApi: ACCESS_CONTROL_MANAGEMENT_ROOT_CONTEXT + "activate-pre-authenticated-api"
            },
            taxManagement: {
                getAllCountries: TAX_MANAGEMENT_ROOT_CONTEXT + "countries",
                getAllTaxCategories: TAX_MANAGEMENT_ROOT_CONTEXT + "categories",
                getAllTaxCategoriesBasicInfo: TAX_MANAGEMENT_ROOT_CONTEXT + "categories/basic",
                addTaxCategory: TAX_MANAGEMENT_ROOT_CONTEXT + "category/add",
                updateTaxCategoy: TAX_MANAGEMENT_ROOT_CONTEXT + "category/update",
                changeTaxCategoyStatus: TAX_MANAGEMENT_ROOT_CONTEXT + "category/status",
                fetchGSTCategoryTax: TAX_MANAGEMENT_ROOT_CONTEXT + "tax",
                getAllTaxTypes: TAX_MANAGEMENT_ROOT_CONTEXT + "tax/type",
                getAllTaxTypesBasicInfo: TAX_MANAGEMENT_ROOT_CONTEXT + "tax/type/basic",
                addTaxData: TAX_MANAGEMENT_ROOT_CONTEXT + "tax/add",
                updateCategoryTax: TAX_MANAGEMENT_ROOT_CONTEXT + "tax/update",
                fetchCategoryAdditionalTax: TAX_MANAGEMENT_ROOT_CONTEXT + "tax/additional",
                updateCategoryAdditionalTax: TAX_MANAGEMENT_ROOT_CONTEXT + "tax/additional/udpate"
            },
            cashCardManagement: {
                getCardByCode: GIFT_CARD_MANAGEMENT_ROOT_CONTEXT + "card/code",
                getCardBySerial: GIFT_CARD_MANAGEMENT_ROOT_CONTEXT + "card/serial",
                activateCard: GIFT_CARD_MANAGEMENT_ROOT_CONTEXT + "activateGiftCard",
                offers: GIFT_CARD_MANAGEMENT_ROOT_CONTEXT + "card/offers",
                addList: GIFT_CARD_MANAGEMENT_ROOT_CONTEXT + "card/offers/add",
                changeStatus: GIFT_CARD_MANAGEMENT_ROOT_CONTEXT + "card/offers/changeStatus",
                offersForDate: GIFT_CARD_MANAGEMENT_ROOT_CONTEXT + "card/offers/date",
            },
            budgetManagement: {
                downloadBudgetTemplate: BUDGET_MANAGEMENT_ROOT_CONTEXT + "budget-sheet",
                downloadBudgetTemplateActual: BUDGET_MANAGEMENT_ROOT_CONTEXT + "budget-sheet/actual",
                uploadBudgetTemplate: BUDGET_MANAGEMENT_ROOT_CONTEXT + "upload-budgets",
                downloadBudgetTemplateManpower: BUDGET_MANAGEMENT_ROOT_CONTEXT + "budget-sheet/manpower",
                downloadBudgetTemplateChannelPartner: BUDGET_MANAGEMENT_ROOT_CONTEXT + "budget-sheet/channel-partner",
                downloadBudgetTemplateBankCharges: BUDGET_MANAGEMENT_ROOT_CONTEXT + "budget-sheet/bank-charges",
                downloadBudgetTemplateFacilityCharges: BUDGET_MANAGEMENT_ROOT_CONTEXT + "budget-sheet/facility-charges",
                downloadBudgetTemplateServiceCharges: BUDGET_MANAGEMENT_ROOT_CONTEXT + "budget-sheet/service-charges",
                uploadBudgetTemplateManpower: BUDGET_MANAGEMENT_ROOT_CONTEXT + "upload-budgets/manpower",
                uploadBudgetTemplateChannelPartner: BUDGET_MANAGEMENT_ROOT_CONTEXT + "upload-budgets/channel-partner",
                uploadBudgetTemplateBankCharges: BUDGET_MANAGEMENT_ROOT_CONTEXT + "upload-budgets/bank-charges",
                uploadBudgetTemplateFacilityCharges: BUDGET_MANAGEMENT_ROOT_CONTEXT + "upload-budgets/facility-charges",
                uploadBudgetTemplateServiceCharges: BUDGET_MANAGEMENT_ROOT_CONTEXT + "upload-budgets/service-charges",
            },
            expenseManagement: {
                addExpense: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/add-expense",
                getExpenseDetail: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/get-expense",
                cancelExpenseDetail: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/cancel-expense",
                addMeterDetails: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/add-meter-reading",
                lastMeterReading: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/last-meter-reading",
                PnLReportMTD: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/MTD-PnL-for-unit",
                PnLAggregateReport: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/get-pnl-representation",
                budgetExceededTransactions: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/budget-exceeded-datas",
                lastMeterReadingList: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/last-meter-reading-list",
                updatelastMeterReading: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/update-last-Reading",
                getMTDPnlDetailSheet: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/get-mtd-pnl-detail",
                getMTDPnlAggregateDetailSheet: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/get-mtd-pnl-aggregate-detail",
                getMTDPnlAggregateDetailSheetDAM: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/get-mtd-pnl-aggregate-detail-DAM",
                getFinalizedPnlDetailSheet: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "/get-finalized-pnl-detail"
            },
            dsrManagement: {
                uploadDsr: POS_METADATA_ROOT_CONTEXT + "/unit/generate/external",
                allpartnerID: POS_METADATA_ROOT_CONTEXT + "/dsr-configuration/get-partnerid",
                updateDsrConfig: POS_METADATA_ROOT_CONTEXT + "/dsr-configuration/update",
                addDsrConfig: POS_METADATA_ROOT_CONTEXT + "/dsr-configuration/add-partner-id",
                getDsrConfig: POS_METADATA_ROOT_CONTEXT + "/dsr-configuration/get-config"
            },
            referenceOrderManagement: {
                UPTCalc: POS_METADATA_ROOT_CONTEXT + "/upt-calculation",
                unitDetail: PRODUCT_METADATA_ROOT_CONTEXT + "/exception-day/unit-detail",
                setExceptionDateEntry: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "/set-exception-date",
                getExceptionDateEntry: REFERENCE_MANAGEMENT_ROOT_CONTEXT + "/get-exception-date"
            },
            appsManagement: {
                latest: APPS_MANAGEMENT_ROOT_CONTEXT + "latest",
                activate: APPS_MANAGEMENT_ROOT_CONTEXT + "activate-version",
                initiateArduinoBuild: APPS_MANAGEMENT_ROOT_CONTEXT + "initiate-arduino-build",
                initiatedArduinoBuild: APPS_MANAGEMENT_ROOT_CONTEXT + "initiated-arduino-build",
                latestArduinoBuild: APPS_MANAGEMENT_ROOT_CONTEXT + "active-arduino-version",
                uploadArduinoBuild: APPS_MANAGEMENT_ROOT_CONTEXT + "upload-arduino-build",
                activateArduinoBuild: APPS_MANAGEMENT_ROOT_CONTEXT + "activate-arduino-build"
            },
            analytics: {
                monthlySalesTarget: ANALYTICS_ROOT_CONTEXT + "units/sales/target/update/monthly"
            },
            partnerManagement: {
                get: PARTNER_MANAGEMENT_CONTEXT + "get",
                add: PARTNER_MANAGEMENT_CONTEXT + "add",
                getActive: PARTNER_MANAGEMENT_CONTEXT + "get-active",
                activate: PARTNER_MANAGEMENT_CONTEXT + "activate",
                deactivate: PARTNER_MANAGEMENT_CONTEXT + "deactivate",
                getPartnerHistory: PARTNER_MANAGEMENT_CONTEXT + "get-partner-history"
            },
            partnerMetadata: {
                unitToggle: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit-toggle",
                itemStockUpdate: PARTNER_METADATA_MANAGEMENT_CONTEXT + "item-stock-update",
                getMenu: PARTNER_METADATA_MANAGEMENT_CONTEXT + "menu-get",
                addMenu: PARTNER_METADATA_MANAGEMENT_CONTEXT + "menu-add",
                refreshMenu: PARTNER_METADATA_MANAGEMENT_CONTEXT + "menu-refresh",
                addMenuForUnit: PARTNER_METADATA_MANAGEMENT_CONTEXT + "menu-add-per-unit",
                getActiveMenuForUnits: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-active-menu-for-units",
                addOffer: PARTNER_METADATA_MANAGEMENT_CONTEXT + "add-offer",
                activateOffer: PARTNER_METADATA_MANAGEMENT_CONTEXT + "activate-offer",
                deactivateOffer: PARTNER_METADATA_MANAGEMENT_CONTEXT + "deactivate-offer",
                getOffers: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-active-offers",
                getZomatoTreatsItem: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-zomato-treats-item",
                setZomatoTreatsItem: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-zomato-treats-item",
                removeZomatoTreatsItem: PARTNER_METADATA_MANAGEMENT_CONTEXT + "remove-zomato-treats-item",
                getOutletLogisticsStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit/logistics-status/get",
                updateOutletLogisticsStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit/logistics-status/update",
                updateOutletDeliveryStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit/delivery-status/update",
                getOutletDeliveryStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit/delivery-status/get",
                updateOutletTakeawayStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit/takeaway-status/update",
                getOutletTakeawayStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit/takeaway-status/get",
                publishInventoryToPartner: PARTNER_METADATA_MANAGEMENT_CONTEXT + "inventory/publish",
                getPartnerProductFilter: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-filter",
                setPartnerProductFilter: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-partner-product-filter",
                getPartnerProductTags: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-tags",
                getPartnerMeatTags: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-meat-tags",
                setPartnerProductTags: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-partner-product-tags",
                setPartnerMeatTags: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-meat-tags",
                getPartnerProductTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-tags-mappings",
                getPartnerProductMeatTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-meat-tags-mappings",
                setPartnerProductTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-partner-product-tags-mappings",
                setPartnerProductMeatTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-partner-product-meat-tags-mappings",
                setPartnerAllergenTags: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-allergen-tags",
                getPartnerAllergenTags: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-allergen-tags",
                getPartnerProductAllergenTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-allergen-tags-mappings",
                setPartnerProductAllergenTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-partner-product-allergen-tags-mappings",
                setPartnerServingInfo: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-serving-info",
                getPartnerServingInfo: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-serving-info",
                getPartnerProductServingInfoTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-serving-info-tags-mappings",
                setPartnerProductServingInfoTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-partner-product-serving-info-tags-mappings",
                setPartnerServingSize: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-serving-size",
                getPartnerServingSize: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-serving-size",
                getPartnerProductServingSizeTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-serving-size-mapping",
                setPartnerProductServingSizeTagsMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-partner-product-serving-size-mapping",
                getPartnerProductServingSizeTagsMappingsDetail: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-serving-size-mapping-detail",
                getPartnerBogoProducts: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-bogo-product",
                setPartnerBogoProducts: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-partner-bogo-product",
                sendPartnerOffer: PARTNER_METADATA_MANAGEMENT_CONTEXT + "send-partner-offer",
                getPartnerProductTagsMappingsDetail: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-tags-mappings-detail",
                getPartnerProductMeatTagsMappingsDetail: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-meat-tags-mappings-detail",
                getPartnerProductAllergenTagsMappingsDetail: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-allergen-tags-mappings-detail",
                getPartnerProductServingInfoTagsMappingsDetail: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-partner-product-serving-info-tags-mappings-detail",
                addSingleServeMenuForUnit: PARTNER_METADATA_MANAGEMENT_CONTEXT + "single-serve-menu-add-per-unit",
                getSwiggyStockStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-swiggy-stock-version-status",
                updateSwiggyStockStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "update-swiggy-stock-version-status",
                addMenuVersion: PARTNER_METADATA_MANAGEMENT_CONTEXT + "add-menu-version",
                getUnitMenuVersions: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-unit-menu-data",
                markUnitVersionDefault: PARTNER_METADATA_MANAGEMENT_CONTEXT + "mark-unit-version-default",
                showVersionMenu: PARTNER_METADATA_MANAGEMENT_CONTEXT + "show-version-menu",
                pushMenuToUnits: PARTNER_METADATA_MANAGEMENT_CONTEXT + "push-menu-to-units",
                setAutoFlag: PARTNER_METADATA_MANAGEMENT_CONTEXT + "set-menu-auto-flag",
                cafeAutoPushData: PARTNER_METADATA_MANAGEMENT_CONTEXT + "menu-auto-push-data",
                menuAutoPushHistory: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-menu-auto-push-history",
                menuAuditHistorys: PARTNER_METADATA_MANAGEMENT_CONTEXT + "menu-audit-history",
                getDcCustomProfiles: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-dc-profiles",
                addDcCustomProfiles: PARTNER_METADATA_MANAGEMENT_CONTEXT + "add-dc-profile",
                getDcCustomProfileMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-dc-profile-mappings",
                addDcCustomProfileMappings: PARTNER_METADATA_MANAGEMENT_CONTEXT + "add-dc-profile-mappings",
                updateDcCustomProfileMappingStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "update-dc-profile-mapping-status",
                getDcCustomProfilesForUnit: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-dc-profiles-for-unit",
                trackSwiggyMenuPush: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-swiggy-menu-status",
                getDOTDProducts: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-dotd-products-list",
                getProductIdsForSplitDimension : PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-product-ids-for-split-dimension",
            },
            channelPartnerCache: {
                reloadPartnersCache: PARTNER_CACHE_MANAGEMENT_CONTEXT + "reload-partner-cache",
                reloadUnitProductProductComboMapping: PARTNER_CACHE_MANAGEMENT_CONTEXT + "reload-unit-product-combo-mapping",
                loadUnitUpsellingSuperComboMapping: PARTNER_CACHE_MANAGEMENT_CONTEXT + "reload-partner-unit-product-mapping",
                loadPartnerUnitProductPricingMap: PARTNER_CACHE_MANAGEMENT_CONTEXT + "reload-partner-unit-product-pricing",
                reloadPartnerStock: PARTNER_CACHE_MANAGEMENT_CONTEXT + "reload-stock",
                reloadPartnerUnitStock: PARTNER_CACHE_MANAGEMENT_CONTEXT + "reload-stock/unit",
                getProductComboMapping: PARTNER_CACHE_MANAGEMENT_CONTEXT + "product-combo/unit",
                getComboProductMapping: PARTNER_CACHE_MANAGEMENT_CONTEXT + "combo-product/unit",
            },
            partnerReports: {
                getUnitProductStockSnapshot: PARTNER_REPORT_CONTEXT + "stock/snapshot"
            },
            brandManagement: {
                getAllBrands: BRAND_METADATA_ROOT_CONTEXT + "/brands",
                getUnitPartnerBrandMapping: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-mapping",
                getAllUnitPartnerBrandMapping: BRAND_METADATA_ROOT_CONTEXT + "/all-unit-partner-brand-mapping",
                addUnitPartnerBrandMapping: BRAND_METADATA_ROOT_CONTEXT + "/add/unit-partner-mapping",
                activateUnitPartnerBrandMapping: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-mapping/activate",
                deactivateUnitPartnerBrandMapping: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-mapping/deactivate",
                toggleUnitChannelPartnerMapping: BRAND_METADATA_ROOT_CONTEXT + "/unit-partner-brand-mapping/toggle-status",
            },
            cache: {
                allMenuSequence: DINE_IN_APP_CACHE_CONTEXT + "/company/all-menu-sequence/",
                allproducts: DINE_IN_APP_CACHE_CONTEXT + "/company/all-products/",
                list: DINE_IN_APP_CACHE_CONTEXT + "/company/list/",
                tax: DINE_IN_APP_CACHE_CONTEXT + "/company/tax/",
                locations: DINE_IN_APP_CACHE_CONTEXT + "/company/locations/",
                productImages: DINE_IN_APP_CACHE_CONTEXT + "/company/product-images/",
                walletProducts: DINE_IN_APP_CACHE_CONTEXT + "/company/wallet-products/",
                walletOffers: DINE_IN_APP_CACHE_CONTEXT + "/company/wallet-offers/",
                locationMetadata: DINE_IN_APP_CACHE_CONTEXT + "/company/location-metadata/",
                orderStateMetadata: DINE_IN_APP_CACHE_CONTEXT + "/company/order-state-metadata/",
                locationData: DINE_IN_APP_CACHE_CONTEXT + "/company/location-data/",
                unitOperations: DINE_IN_APP_CACHE_CONTEXT + "/company/unit-operations/",
                unitOffer: DINE_IN_APP_CACHE_CONTEXT + "/company/unit-offer/",
                unitAppBlockers: DINE_IN_APP_CACHE_CONTEXT + "/company/unit-app-blockers/",
                unitAlliances: DINE_IN_APP_CACHE_CONTEXT + "/company/unit-alliances/",
                unitBanners: DINE_IN_APP_CACHE_CONTEXT + "/company/unit-banners/",
                walletBanners: DINE_IN_APP_CACHE_CONTEXT + "/company/wallet-banners/",
                unitPromotions: DINE_IN_APP_CACHE_CONTEXT + "/company/unit-promotions/",
                unitChaayosGiftings: DINE_IN_APP_CACHE_CONTEXT + "/company/unit-chaayos-gifting/",
                allUnits: DINE_IN_APP_CACHE_CONTEXT + "/company/all-units/",
                all: DINE_IN_APP_CACHE_CONTEXT + "/company/all/",
                refreshWebTags: DINE_IN_APP_CACHE_CONTEXT + "/external/refreshWebTags/",
                refreshUnit: DINE_IN_APP_CACHE_CONTEXT + "/company/refresh-unit/",
                appShufflingCategorySequenceData: DINE_IN_APP_CACHE_CONTEXT + "/company/category-sequence-data/",
                refreshSequenceData: DINE_IN_APP_CACHE_CONTEXT + "/company/sequence/"
            },
            dineInAppCrm: {
                clearAllCustomerOffers: DINE_IN_APP_CUSTOMER_OONTEXT + "/cu-off-clear-all/"
            },
            appNotification: {
                notificationType: NOTIFICATION_ROOT_CONTEXT + "/notification-type",
                notificationCategory: NOTIFICATION_ROOT_CONTEXT + "/notification-category",
                notificationActionType: NOTIFICATION_ROOT_CONTEXT + "/notification-action-type",
                notificationTopic: NOTIFICATION_ROOT_CONTEXT + "/notification-topic",
                addNotificationRequest: NOTIFICATION_ROOT_CONTEXT + "/add-notification-request",
                getNotificationRequest: NOTIFICATION_ROOT_CONTEXT + "/get-notification-request",
                getTestAccounts: NOTIFICATION_ROOT_CONTEXT + "/get-test-accounts",
                addTestAccounts: NOTIFICATION_ROOT_CONTEXT + "/add-test-accounts",
                testNotification: NOTIFICATION_ROOT_CONTEXT + "/test-notification",
                sendNotification: NOTIFICATION_ROOT_CONTEXT + "/send-notification",
                saveFile: NOTIFICATION_ROOT_CONTEXT + "/save-file"
            },
            location: {
                loadLocation: DINE_IN_APP_LOCATION_CONTEXT + "/load-locations",
                addLocation: MASTER_METADATA_ROOT_CONTEXT + "add-location"
            },
            bannerManagement: {
                uploadBanner: BANNER_ROOT_CONTEXT + "/upload-banner",
                saveBanner: BANNER_ROOT_CONTEXT + "/save-banner",
                getBanner: BANNER_ROOT_CONTEXT + "/get-banner",
                getBannerType: BANNER_ROOT_CONTEXT + "/get-banner-type",
                getSectionType: BANNER_ROOT_CONTEXT + "/get-section-type",
                getBannerActionType: BANNER_ROOT_CONTEXT + "/get-banner-action-type",
                updateBannerStatus: BANNER_ROOT_CONTEXT + "/update-banner-status",
            },
            faceRecognition: {
                deRegisterCustomer: REKOG_SERVICE + "face-it/opt-out-customer/contact-number"
            },
            orderManagement: {
                getOrderByOrderId: KETTLE_ORDER_ROOT_CONTEXT + "order/generated-order",
                cancelOrder: KETTLE_ORDER_ROOT_CONTEXT + "order/cancel",
                updateOrderStatus: KETTLE_ORDER_ROOT_CONTEXT + "order/settle-order",
                deliveryPartners: TRANSACTION_METADATA_ROOT_CONTEXT + "unit-metadata",
                getMonthlyOrdersOfRecipe: KETTLE_ORDER_ROOT_CONTEXT + "get-monthly-orders-of-recipe",
                customerMappingTypes: KETTLE_ORDER_ROOT_CONTEXT + "customer-mapping-types",
                getOrderPaymentDetail : KETTLE_ORDER_ROOT_CONTEXT + "get-order-payment-management",
                getLoyaltyEvents : KETTLE_ORDER_ROOT_CONTEXT + "get-loyalty-events",
                orderRefund : KETTLE_ORDER_ROOT_CONTEXT + "order/refund",
                getOrderPaymentDetailsBySourceAndStatus : KETTLE_ORDER_ROOT_CONTEXT + "order-payment-detail",
                resetPrintCount : STORE_OPS_ROOT_CONTEXT + "order-management/reset-print-count"
            },
            partnerOrder: {
                markCancelled: PARTNER_ORDER_ROOT_CONTEXT + "mark-cancelled"
            },
            paymentManagement: {
                addPaymentMapping: PAYMENT_MANAGEMENT_ROOT_CONTEXT + "add/payment-mapping",
                getPaymentMapping: PAYMENT_MANAGEMENT_ROOT_CONTEXT + "get-all/payment-mapping",
                updatePaymentMapping: PAYMENT_MANAGEMENT_ROOT_CONTEXT + "update/payment-mapping",
                updatePaymentModeMapping: PAYMENT_MANAGEMENT_ROOT_CONTEXT + "update-all/payment-mapping",
                getPaymentModeMapping: PAYMENT_MANAGEMENT_ROOT_CONTEXT + "get-all-unit/payment-mapping"
            },
            priceManagement: {
                getUnitProductPriceSheet: PRICE_MANAGEMENT_ROOT_CONTEXT + "/get-unit-product-price-sheet",
                updateBulkUnitProductPrice: PRICE_MANAGEMENT_ROOT_CONTEXT + "/product/price/bulk-update"
            },
            recipeMediaManagement: {
                downloadRecipeMediaByStep: RECIPE_MEDIA_MANAGEMENT_ROOT_CONTEXT + "/download-recipe-media-by-step"
            },
            droolsFileManagement: {
                getAllDroolFileForType: DROOL_MANAGEMENT_ROOT_CONTEXT + "get-all-file",
                activateDroolFile: DROOL_MANAGEMENT_ROOT_CONTEXT + "activate-version",
                downLoadDroolFile: DROOL_MANAGEMENT_ROOT_CONTEXT + "download-decision-sheet",
                addNewFile: DROOL_MANAGEMENT_ROOT_CONTEXT + "reset-offer-decision-drool",
                setAsDefault: DROOL_MANAGEMENT_ROOT_CONTEXT + "set-default-drool",
            },
            droolsCrmFileManagement: {
                getAllDroolFileForType: DROOL_CRM_MANAGEMENT_ROOT_CONTEXT + "get-all-file",
                activateDroolFile: DROOL_CRM_MANAGEMENT_ROOT_CONTEXT + "activate-version",
                downLoadDroolFile: DROOL_CRM_MANAGEMENT_ROOT_CONTEXT + "download-decision-sheet",
                addNewFile: DROOL_CRM_MANAGEMENT_ROOT_CONTEXT + "reset-offer-decision-drool",
                setAsDefault: DROOL_CRM_MANAGEMENT_ROOT_CONTEXT + "set-default-drool",
                inActivateFile: DROOL_CRM_MANAGEMENT_ROOT_CONTEXT + "inactive-version"
            },
            droolsTransactionManagement: {
                getAllDroolFileForType: DROOL_TRANSACTION_ROOT_CONTEXT + "get-all-file",
                activateDroolFile: DROOL_TRANSACTION_ROOT_CONTEXT + "activate-version",
                downLoadDroolFile: DROOL_TRANSACTION_ROOT_CONTEXT + "download-decision-sheet",
                addNewFile: DROOL_TRANSACTION_ROOT_CONTEXT + "reset-offer-decision-drool",
                setAsDefault: DROOL_TRANSACTION_ROOT_CONTEXT + "set-default-drool",
            },
            b2bMonkManagement: {
                addCustomer: B2B_MONK_USER_MANAGEMENT_CONTEXT + "add-new-customer",
                addUser: B2B_MONK_USER_MANAGEMENT_CONTEXT + "add-new-user",
                getAllCustomers: B2B_MONK_USER_MANAGEMENT_CONTEXT + "get-customers",
                getAllUsers: B2B_MONK_USER_MANAGEMENT_CONTEXT + "get-active-users",
                getOrderDetailsByUsername: B2B_MONK_USER_MANAGEMENT_CONTEXT + "order-detail-by-user",
                getActionDetailsByUsername: B2B_MONK_USER_MANAGEMENT_CONTEXT + "action-detail-by-user",
                updateCustomer: B2B_MONK_USER_MANAGEMENT_CONTEXT + "update-customer",
                updateUser: B2B_MONK_USER_MANAGEMENT_CONTEXT + "update-user",
                getErrorDetailsByUsername: B2B_MONK_ACTION_MANAGEMENT_CONTEXT + "error-detail-by-user",
                getCustomersByName: B2B_MONK_USER_MANAGEMENT_CONTEXT + "get-customers-by-name",
                getUsersByCustomer: B2B_MONK_USER_MANAGEMENT_CONTEXT + "get-users-by-customer",
            },
            dataEncryption: {
                encrypt: DATA_ENCRYPTION_ROOT_CONTEXT + "encrypt",
            },
            serverRestart: {
                appCache: DINE_IN_APP_CACHE_CONTEXT + "/restart",
                appCrm: NOTIFICATION_CONTEXT + "config/restart",
            },
            storeManagement :{
                testCampaignNotification: STORE_MANAGEMENT_ROOT_CONTEXT + "test-campaign-notification",
                customerMappingTypes: STORE_MANAGEMENT_ROOT_CONTEXT + "customer-mapping-types",

            },
            chaiMonkDashboard: {
                            realTime: ANALYTICS_ROOT_CONTEXT + "log/assembly/dashboard/rt",
                            assemblyNotification : ANALYTICS_ROOT_CONTEXT + "log/send/assembly-notification"
            },
            unitClosure : {
                initiateClosure: UNIT_METADATA_ROOT_CONTEXT+"/closure/initiate",
                getClosureStatus: UNIT_METADATA_ROOT_CONTEXT+"/get-closure-status",
                getClosureByStatus: UNIT_METADATA_ROOT_CONTEXT+"/closure/get-by-status",
                getClosureByStates: UNIT_METADATA_ROOT_CONTEXT+"/closure/get-by-state",
                checkStateSCM: ATTRIBUTE_MANAGEMENT_ROOT_CONTECT+"/validate-state",
                closeUnitClosure : UNIT_METADATA_ROOT_CONTEXT + "/closure/close-event",
                getClosureFormMetaData : UNIT_METADATA_ROOT_CONTEXT + "/get-closure-form-metadata",
                addUnitClosureFormData : UNIT_METADATA_ROOT_CONTEXT + "/add-unit-closure-form-data",
                sendPendingTaskNotificaton : UNIT_METADATA_ROOT_CONTEXT + "/send-unit-closure-pending-task-notification"
            },
            winback : {
                getDineInCoupon : CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/get/winback/offer/dineIn",
                getDeliveryCoupn : CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/get/winback/offer/delivery",
                generateCoupon : CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/generate/winback/coupon",
                notify : CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/mark-notified",
                compensationReason : UNIT_METADATA_ROOT_CONTEXT + "/listTypes/compensationReason",
                getWinbackCouponList : CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/get/winback/info",
                downloadSheet : CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT+ "/download/winback/sheet/all",
                downloadSheetBetweenDates : CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "/download/winback/sheet"
            },
            employeeManagement : {
                syncEmployee : EMPLOYEE_MANAGEMENT_ROOT_CONTEXT + "/syncEmployeeData"
            },
            masterMetaData: {
                getCategoryAttributes : MASTER_CONTEXT + "metadata/get-category-attributes",
                updateCategoryAttributes : MASTER_CONTEXT + "metadata/update-category-attributes",
                uploadCategoryImage : MASTER_CONTEXT + "metadata/upload-category-images",
                uploadMenuMappings : MASTER_CONTEXT + "metadata/upload-menu-excel",
                downloadMenuMappingsSheet : MASTER_CONTEXT + "metadata/menu-mapping-excel/download",
                getMenuMappingsEvent : MASTER_CONTEXT + "metadata/menu-mapping-excel/event"
            },
            unitDroolMetadata: {
                unitDroolMapping : UNIT_DROOL_METADATA_ROOT_CONTEXT + "/unit-drool-version-mapping",
                updateUnitDroolMapping : UNIT_DROOL_METADATA_ROOT_CONTEXT + "/update-unit-drool-mapping"
            }
        };

        return service;
    }

})();
