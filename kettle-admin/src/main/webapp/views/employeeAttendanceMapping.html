
<link rel="stylesheet" href="css/employee_attendance_mapping.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
<div class="app-container" data-ng-init="init()" ng-controller="employeeAttendanceMappingCtrl">
    <div class="container">
        <div class="header">
            <h1>Employee Eligibility Mapping</h1>
            <p>Configure attendance and approval eligibility mappings for employees</p>
        </div>

        <div class="step-indicator">
            <div class="step active" id="step1">
                <div class="step-number">1</div>
                <div class="step-label">Select Employees</div>
            </div>
            <div class="step-connector"></div>
            <div class="step" id="step2">
                <div class="step-number">2</div>
                <div class="step-label">Configure Mappings</div>
            </div>
            <div class="step-connector"></div>
            <div class="step" id="step3">
                <div class="step-number">3</div>
                <div class="step-label">Review & Save</div>
            </div>
        </div>

        <div class="main-content">
            <!-- Step 1: Employee Selection -->
            <div class="employee-selection" id="employeeSelection" ng-show="currentStep === 1">
                <div class="search-section">
                    <div class="search-title">🔍 Filter Employees</div>
                    <div class="filters-row">
                        <div class="form-group">
                            <label>Department</label>
                            <div class="searchable-dropdown-container">
                                <input type="text" class="searchable-input" placeholder="Search departments..."
                                       ng-model="departmentSearch" ng-focus="showDepartmentDropdown = true"
                                       ng-blur="hideDepartmentDropdown()" ng-keyup="filterDepartments()">
                                <div class="dropdown-options" ng-class="{show: showDepartmentDropdown}">
                                    <div class="dropdown-option" ng-click="selectDepartment({name: 'All Departments'})"
                                         ng-mousedown="$event.preventDefault()">
                                        All Departments
                                    </div>
                                    <div class="dropdown-option" ng-repeat="dept in filteredDepartments"
                                         ng-click="selectDepartment(dept)" ng-mousedown="$event.preventDefault()">
                                        {{dept.name || dept.departmentName || dept}}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>Designation</label>
                            <div class="searchable-dropdown-container">
                                <input type="text" class="searchable-input" placeholder="Search designations..."
                                       ng-model="designationSearch" ng-focus="showDesignationDropdown = true"
                                       ng-blur="hideDesignationDropdown()" ng-keyup="filterDesignations()">
                                <div class="dropdown-options" ng-class="{show: showDesignationDropdown}">
                                    <div class="dropdown-option" ng-click="selectDesignation({name: 'All Designations'})"
                                         ng-mousedown="$event.preventDefault()">
                                        All Designations
                                    </div>
                                    <div class="dropdown-option" ng-repeat="designation in filteredDesignations"
                                         ng-click="selectDesignation(designation)" ng-mousedown="$event.preventDefault()">
                                        {{designation.name || designation.designationName || designation}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="search-btn" ng-click="searchEmployees()">🔍 Search</button>
                        <button class="bulk-btn" ng-click="exportAllEmployeeMappings()" style="width: 200px; height: 50px; margin:auto;">📊 Export Current</button>
                        <div class="form-group">


                        </div>
                    </div>
                </div>

                <div class="employee-list">
                    <div class="list-header">
                        <div style="display: flex; align-items: center; gap: 12px;">
                            <div class="list-title">👥 Employee List</div>
                            <input type="text" class="form-control" placeholder="Search by name..." ng-model="searchText" ng-change="searchEmployees()" style="width: 200px; margin: 0;">
                        </div>
                        <div class="select-all" ng-click="toggleSelectAll()">
                            <input type="checkbox" ng-checked="selectedEmployees.length === filteredEmployees.length && filteredEmployees.length > 0"> Select All ({{filteredEmployees.length}} employees)
                        </div>
                    </div>

                    <!-- Employee Count Info -->
                    <div class="employee-count-info">
                        <span class="count-badge">{{filteredEmployees.length}}</span> employees found
                        <span ng-show="filteredEmployees.length !== employees.length"> (filtered from {{employees.length}} total)</span>
                    </div>

                    <div class="employee-item" ng-repeat="employee in paginatedEmployees">
                        <input type="checkbox" class="employee-checkbox" ng-checked="isEmployeeSelected(employee)" ng-click="toggleEmployeeSelection(employee)">
                        <div class="employee-info">
                            <div class="employee-name">{{employee.name || employee.firstName + ' ' + employee.lastName}}</div>
                            <div class="employee-details">Employee Code: {{employee.employeeCode}} • Employee ID: {{ employee.id }}• {{employee.designation || employee.designationName || 'N/A'}} • {{employee.department || employee.departmentName || 'N/A'}}</div>
                        </div>
                        <div class="employee-actions">
                            <div class="mapping-count pull-right">Mapped to :  </div>
                            <br/>
                            <i class="fa fa-eye view-mapping-icon" ng-click="viewEmployeeMappings(employee)" title="View Mappings" style="cursor: pointer; color: #3498db; font-size: 16px;"></i>
                        </div>

                    </div>

                    <div ng-if="filteredEmployees.length === 0" class="empty-state">
                        <div class="empty-icon">👥</div>
                        <div class="empty-text">No employees found</div>
                        <div class="empty-subtext">Try adjusting your search or filter criteria</div>
                    </div>

                    <!-- Pagination -->
                    <div class="pagination-container" ng-show="filteredEmployees.length > 0">
                        <div class="pagination-info">
                            Showing {{getStartIndex()}} to {{getEndIndex()}} of {{filteredEmployees.length}} employees
                        </div>
                        <div class="pagination-controls">
                            <button class="pagination-btn" ng-click="goToPage(1)" ng-disabled="currentPage === 1">«</button>
                            <button class="pagination-btn" ng-click="goToPage(currentPage - 1)" ng-disabled="currentPage === 1">‹</button>

                            <button class="pagination-btn" ng-repeat="page in getVisiblePages()"
                                    ng-click="goToPage(page)" ng-class="{active: page === currentPage}"
                                    ng-show="page !== '...'">
                                {{page}}
                            </button>
                            <span class="pagination-ellipsis" ng-repeat="page in getVisiblePages()" ng-show="page === '...'">...</span>

                            <button class="pagination-btn" ng-click="goToPage(currentPage + 1)" ng-disabled="currentPage === totalPages">›</button>
                            <button class="pagination-btn" ng-click="goToPage(totalPages)" ng-disabled="currentPage === totalPages">»</button>
                        </div>
                    </div>
                </div>

                <div class="selected-summary">
                    <div class="employees-chips">
                        <span ng-repeat="employee in selectedEmployees | limitTo: 14" class="employee-chip">
                            {{employee.name || employee.firstName + ' ' + employee.lastName}} ({{employee.employeeId || employee.id}})
                        </span>
                        <span ng-if="selectedEmployees.length > 14" class="employee-chip">
                            ... and {{selectedEmployees.length - 14}} more
                        </span>
                        <div ng-if="selectedEmployees.length === 0" class="empty-chips">
                            No employees selected
                        </div>
                    </div>
                    <!-- <button class=" next-btn" ng-click=" "> Clone </button>-->
                    <button class="next-btn" ng-click="goToMapping()">Next Step →</button>
                </div>
            </div>

            <!-- Step 2: Mapping Configuration -->
            <div class="mapping-section" id="mappingSection" ng-show="currentStep === 2">
                <div class="eligibility-tabs">
                    <button class="tab-btn active" ng-click="switchEligibilityTab('attendance')">
                        📅 Attendance Eligibility
                    </button>
                    <button class="tab-btn" ng-click="switchEligibilityTab('approval')">
                        ✅ Approval Eligibility
                    </button>
                </div>

                <div class="eligibility-content" id="attendanceTab">
                    <div class="mapping-tabs">
                        <button class="mapping-tab"
                                ng-class="{active: selectedMappingType === 'unit'}"
                                ng-click="switchMappingTab('unit')">Unit Mapping</button>

                        <button class="mapping-tab"
                                ng-class="{disabled: !isMultiMappingAllowed()}"
                                ng-click="isMultiMappingAllowed() && switchMappingTab('city')">City Mapping</button>

                        <button class="mapping-tab"
                                ng-class="{disabled: !isMultiMappingAllowed()}"
                                ng-click="isMultiMappingAllowed() && switchMappingTab('region')">Region Mapping</button>
                    </div>

                    <div class="mapping-form">
                        <div class="search-title">🏢 Configure Unit Mapping</div>

                        <!-- Unit Filters Section -->
                        <div class="unit-filters-section" ng-show="selectedMappingType === 'unit'">
                            <div class="unit-filters-title">
                                🔍 Filter Units
                                <button class="clear-filters-btn" ng-click="clearUnitFilters()" title="Clear all filters">Clear Filters</button>
                            </div>
                            <div class="filter-row">
                                <div class="form-group">
                                    <label>Category</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search categories..."
                                               ng-model="categorySearch" ng-focus="showCategoryDropdown = true"
                                               ng-blur="hideCategoryDropdown()" ng-keyup="filterCategories()">
                                        <div class="dropdown-options" ng-class="{show: showCategoryDropdown}">
                                            <div class="dropdown-option" ng-repeat="category in filteredCategories"
                                                 ng-click="selectCategory(category)" ng-mousedown="$event.preventDefault()">
                                                {{category.name}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>City</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search cities..."
                                               ng-model="citySearch" ng-focus="showCityDropdown = true"
                                               ng-blur="hideCityDropdown()" ng-keyup="filterCities()">
                                        <div class="dropdown-options" ng-class="{show: showCityDropdown}">
                                            <div class="dropdown-option" ng-repeat="city in filteredCities"
                                                 ng-click="selectCity(city)" ng-mousedown="$event.preventDefault()">
                                                {{city.name || city}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Region</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search regions..."
                                               ng-model="regionSearch" ng-focus="showRegionDropdown = true"
                                               ng-blur="hideRegionDropdown()" ng-keyup="filterRegions()">
                                        <div class="dropdown-options" ng-class="{show: showRegionDropdown}">
                                            <div class="dropdown-option" ng-repeat="region in filteredRegions"
                                                 ng-click="selectRegion(region)" ng-mousedown="$event.preventDefault()">
                                                {{region.name || region}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Unit Zones</label>
                                    <div class="searchable-dropdown-container">
                                        <input type="text" class="searchable-input" placeholder="Search Unit Zones..."
                                               ng-model="unitZoneSearch" ng-focus="showUnitZoneDropdown = true"
                                               ng-blur="hideUnitZoneDropdown()" ng-keyup="filterUnitZones()">
                                        <div class="dropdown-options" ng-class="{show: showUnitZoneDropdown}">
                                            <div class="dropdown-option" ng-repeat="unitZone in filteredUnitZones"
                                                 ng-click="selectUnitZone(unitZone)" ng-mousedown="$event.preventDefault()">
                                                {{unitZone.name}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="filter-info" ng-show="filteredUnits.length !== allUnits.length">
                                📊 Showing {{filteredUnits.length}} of {{allUnits.length}} units based on your filters
                            </div>
                        </div>

                        <!-- Unit Selection Row -->
                        <div class="unit-selection-row" ng-show="selectedMappingType === 'unit'">
                            <div class="form-group">
                                <label>Units <span style="color: #e74c3c;">*</span></label>
                                <div class="searchable-dropdown-container">
                                    <input type="text" class="searchable-input" placeholder="Search units..."
                                           ng-model="unitSearch" ng-focus="showUnitDropdown = true"
                                           ng-blur="hideUnitDropdown()" ng-keyup="filterUnitsDropdown()">
                                    <div class="dropdown-options" ng-class="{show: showUnitDropdown}">
                                        <div class="dropdown-option" ng-repeat="unit in filteredUnitsDropdown"
                                             ng-click="selectUnit(unit)" ng-mousedown="$event.preventDefault()">
                                            {{unit.id}} - {{unit.name}}
                                        </div>
                                    </div>
                                </div>
                                <div class="unit-info-display" ng-show="unitFilters.selectedUnitId">
                                    Selected: {{unitFilters.selectedUnitId}} - {{unitFilters.selectedUnitName}}
                                </div>
                            </div>
                        </div>

                        <!-- City mapping inputs -->
                        <div class="form-row" ng-show="selectedMappingType === 'city'">
                            <div class="form-group">
                                <label>Select City <span style="color: #e74c3c;">*</span></label>
                                <div class="searchable-dropdown-container">
                                    <input type="text" class="searchable-input" placeholder="Search cities..."
                                           ng-model="cityMappingValue" ng-focus="showCityMappingDropdown = true"
                                           ng-blur="hideCityMappingDropdown()" ng-keyup="filterCities()" readonly>
                                    <div class="dropdown-options" ng-class="{show: showCityMappingDropdown}">
                                        <div class="dropdown-option" ng-repeat="city in filteredCities"
                                             ng-click="selectCityMapping(city)" ng-mousedown="$event.preventDefault()">
                                            {{city.name || city}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Region mapping inputs -->
                        <div class="form-row" ng-show="selectedMappingType === 'region'">
                            <div class="form-group">
                                <label>Select Region <span style="color: #e74c3c;">*</span></label>
                                <div class="searchable-dropdown-container">
                                    <input type="text" class="searchable-input" placeholder="Search regions..."
                                           ng-model="regionMappingValue" ng-focus="showRegionMappingDropdown = true"
                                           ng-blur="hideRegionMappingDropdown()" ng-keyup="filterRegions()" readonly>
                                    <div class="dropdown-options" ng-class="{show: showRegionMappingDropdown}">
                                        <div class="dropdown-option" ng-repeat="region in filteredRegions"
                                             ng-click="selectRegionMapping(region)" ng-mousedown="$event.preventDefault()">
                                            {{region.name || region}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Apply To Section -->
                        <div class="form-row">
                            <div class="form-group">
                                <label>Apply To</label>
                                <select class="form-control" ng-model="applyTo" id="applyToSelect" ng-change="toggleIndividualSelection()">
                                    <option value="all">All Selected Employees</option>
                                    <option value="individual">Individual Selection</option>
                                </select>
                            </div>
                        </div>

                        <!-- Individual Selection Panel -->
                        <div class="individual-selection-panel" id="individualSelectionPanel" ng-show="applyTo === 'individual'">
                            <div class="search-title">👤 Select Individual Employees</div>
                            <div class="individual-employees">
                                <div ng-repeat="employee in selectedEmployees" class="individual-employee-item">
                                    <input type="checkbox" ng-model="employee.selectedForMapping">
                                    <span>{{employee.name || employee.firstName + ' ' + employee.lastName}} ({{employee.employeeId || employee.id}})</span>
                                </div>
                            </div>
                        </div>

                        <button class="add-mapping-btn" ng-click="addMapping()">➕ Add Mapping</button>
                    </div>

                    <div class="current-mappings">
                        <div class="mappings-header">
                            <div class="search-title">📋 To Be Mapped</div>
                            <div class="mappings-controls">
                                <div class="mappings-count">{{mappings.length}} mappings</div>
                                <div class="mappings-actions">
                                    <input type="text" class="mapping-search" placeholder="🔍 Search mappings..." id="mappingSearch" ng-keyup="filterMappings()">
                                    <select class="mapping-filter" ng-model="mappingFilter" id="mappingFilter" ng-change="filterMappings()">
                                        <option value="all">All Types</option>
                                        <option value="unit">Unit Only</option>
                                        <option value="city">City Only</option>
                                        <option value="region">Region Only</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mappings-list-compact">
                            <div class="compact-mapping-item" ng-repeat="mapping in mappings" data-type="{{mapping.type}}" data-value="{{mapping.value.toLowerCase()}}" data-employees="{{getEmployeeNames(mapping.employees).toLowerCase()}}">
                                <div class="compact-mapping-content">
                                    <span class="compact-employee-info">
                                        <span ng-if="mapping.employees.length === 1">{{mapping.employees[0].name || mapping.employees[0].firstName + ' ' + mapping.employees[0].lastName}}</span>
                                        <span ng-if="mapping.employees.length > 1" title="{{getEmployeeNames(mapping.employees)}}">{{mapping.employees.length}} Employees</span>
                                    </span>
                                    <span class="mapping-arrow">→</span>
                                    <span class="compact-mapping-target">
                                        <span class="compact-type-badge" ng-class="{'unit-badge': mapping.type === 'unit', 'city-badge': mapping.type === 'city', 'region-badge': mapping.type === 'region'}">{{mapping.type.toUpperCase()}}</span>
                                        <span class="compact-mapping-value">{{mapping.name || mapping.value}}</span>
                                    </span>
                                </div>
                                <div class="compact-mapping-actions">
                                    <button class="compact-edit-btn" ng-click="editMapping($index)" title="Edit Mapping">✏️</button>
                                    <button class="compact-remove-btn" ng-click="removeMapping($index)" title="Remove Mapping">🗑️</button>
                                </div>
                            </div>
                        </div>

                        <div class="no-mappings" ng-show="mappings.length === 0">
                            <div class="empty-state">
                                <div class="empty-icon">📝</div>
                                <div class="empty-text">No mappings </div>
                                <div class="empty-subtext">Add some mappings to get started</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="back-btn" ng-click="goBackToEmployeeSelection()">← Back</button>
                    <div class="bulk-actions">
                        <button class="bulk-btn">📤 Bulk Upload</button>
                        <button class="bulk-btn">📥 Download Template</button>
                    </div>

                    <button class="save-btn" ng-click="saveMappings()">💾 Save All Mappings</button>
                </div>
            </div>

            <!-- Step 3: Review & Save -->
            <div class="review-section" id="reviewSection" ng-show="currentStep === 3">
                <div class="review-header">
                    <div class="search-title">📋 Review & Confirm Mappings</div>
                    <p class="review-subtitle">Please review the mappings below and confirm to save them.</p>
                </div>

                <div class="review-summary">
                    <div class="summary-card">
                        <div class="summary-header">
                            <h3>📊 Summary</h3>
                        </div>
                        <div class="summary-stats">
                            <div class="stat-item">
                                <div class="stat-number">{{selectedEmployees.length}}</div>
                                <div class="stat-label">Selected Employees</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{mappings.length}}</div>
                                <div class="stat-label">Total Mappings</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{eligibilityType}}</div>
                                <div class="stat-label">Eligibility Type</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="review-mappings">
                    <div class="mappings-header">
                        <div class="search-title">🔗 Mapping Details</div>
                    </div>

                    <div class="mapping-review-list">
                        <div class="mapping-review-item" ng-repeat="mapping in mappings">
                            <div class="mapping-type-badge" ng-class="{'unit-badge': mapping.type === 'unit', 'city-badge': mapping.type === 'city', 'region-badge': mapping.type === 'region'}">
                                {{mapping.type.toUpperCase()}}
                            </div>
                            <div class="mapping-details">
                                <div class="mapping-target">
                                    <strong>{{mapping.name || mapping.value}}</strong>
                                </div>
                                <div class="mapping-employees-info">
                                    <span class="employee-count">{{mapping.employees.length}} employee(s):</span>
                                    <span class="employee-list">{{getEmployeeNames(mapping.employees)}}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="no-mappings" ng-show="mappings.length === 0">
                        <div class="empty-state">
                            <div class="empty-icon">📝</div>
                            <div class="empty-text">No mappings to review</div>
                            <div class="empty-subtext">Go back to add some mappings</div>
                        </div>
                    </div>
                </div>

                <div class="review-actions">
                    <button class="back-btn" ng-click="goBackToMapping()">← Back to Mappings</button>
                    <button class="confirm-btn" ng-click="confirmMappings()" ng-disabled="mappings.length === 0">✅ Confirm & Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Employee Mapping View Modal -->
    <div class="modal-overlay" ng-show="showEmployeeMappingModal" ng-click="closeEmployeeMappingModal()">
        <div class="modal-content" ng-click="$event.stopPropagation()">
            <div class="modal-header">
                <h3>👥 Employee Mappings - {{selectedEmployeeForView.name || selectedEmployeeForView.firstName + ' ' + selectedEmployeeForView.lastName}}</h3>
                <button class="modal-close" ng-click="closeEmployeeMappingModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div ng-show="loadingEmployeeMappings" class="loading-state">
                    <div class="spinner"></div>
                    <p>Loading mappings...</p>
                </div>
                <div ng-show="!loadingEmployeeMappings">
                    <div ng-show="employeeMappings.length > 0" class="mappings-container-compact">
                        <div class="compact-mapping-row" ng-repeat="mapping in employeeMappings">
                            <span class="compact-badge" ng-class="{'unit-badge': mapping.mappingType === 'UNIT', 'city-badge': mapping.mappingType === 'CITY', 'region-badge': mapping.mappingType === 'REGION'}">
                                {{mapping.mappingType}}
                            </span>
                            <span class="compact-eligibility">{{mapping.eligibilityType}}</span>
                            <span class="compact-value">{{mapping.displayValue}}</span>
                            <span class="compact-status" ng-class="{'active': mapping.status === 'ACTIVE', 'inactive': mapping.status === 'IN_ACTIVE'}">{{mapping.status}}</span>
                            <span class="compact-creator">by {{mapping.createdBy}}</span>
                        </div>
                    </div>
                    <div ng-show="employeeMappings.length === 0" class="empty-mappings">
                        <div class="empty-icon">📋</div>
                        <div class="empty-text">No mappings found</div>
                        <div class="empty-subtext">This employee has no eligibility mappings configured.</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" ng-click="closeEmployeeMappingModal()">Close</button>
            </div>
        </div>
    </div>
</div>